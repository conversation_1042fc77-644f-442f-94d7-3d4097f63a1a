import Joi from 'joi';

export const userValidationSchemas = {
  register: Joi.object({
    name: Joi.string()
      .min(2)
      .max(50)
      .required()
      .messages({
        'string.min': 'İsim en az 2 karakter olmalıdır',
        'string.max': 'İsim en fazla 50 karakter olmalıdır',
        'any.required': 'İsim alanı zorunludur'
      }),
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': 'Geçerli bir email adresi giriniz',
        'any.required': 'Email alanı zorunludur'
      }),
    password: Joi.string()
      .min(6)
      .required()
      .messages({
        'string.min': 'Şifre en az 6 karakter olmalıdır',
        'any.required': '<PERSON><PERSON>re alanı zorunludur'
      }),
    confirmPassword: Joi.string()
      .valid(Joi.ref('password'))
      .required()
      .messages({
        'any.only': '<PERSON><PERSON><PERSON><PERSON> eşleşmiyor',
        'any.required': '<PERSON><PERSON><PERSON> tekrarı zorunludur'
      })
  }),

  login: Joi.object({
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': 'Geçerli bir email adresi giriniz',
        'any.required': 'Email alanı zorunludur'
      }),
    password: Joi.string()
      .required()
      .messages({
        'any.required': 'Şifre alanı zorunludur'
      })
  }),

  forgotPassword: Joi.object({
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': 'Geçerli bir email adresi giriniz',
        'any.required': 'Email alanı zorunludur'
      })
  }),

  resetPassword: Joi.object({
    password: Joi.string()
      .min(6)
      .required()
      .messages({
        'string.min': 'Şifre en az 6 karakter olmalıdır',
        'any.required': 'Şifre alanı zorunludur'
      }),
    confirmPassword: Joi.string()
      .valid(Joi.ref('password'))
      .required()
      .messages({
        'any.only': 'Şifreler eşleşmiyor',
        'any.required': 'Şifre tekrarı zorunludur'
      })
  }),

  updateProfile: Joi.object({
    name: Joi.string()
      .min(2)
      .max(50)
      .messages({
        'string.min': 'İsim en az 2 karakter olmalıdır',
        'string.max': 'İsim en fazla 50 karakter olmalıdır'
      }),
    email: Joi.string()
      .email()
      .messages({
        'string.email': 'Geçerli bir email adresi giriniz'
      })
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string()
      .required()
      .messages({
        'any.required': 'Mevcut şifre zorunludur'
      }),
    newPassword: Joi.string()
      .min(6)
      .required()
      .messages({
        'string.min': 'Yeni şifre en az 6 karakter olmalıdır',
        'any.required': 'Yeni şifre alanı zorunludur'
      }),
    confirmNewPassword: Joi.string()
      .valid(Joi.ref('newPassword'))
      .required()
      .messages({
        'any.only': 'Şifreler eşleşmiyor',
        'any.required': 'Şifre tekrarı zorunludur'
      })
  })
}; 