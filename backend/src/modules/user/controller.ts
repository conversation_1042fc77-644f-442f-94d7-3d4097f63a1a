import { Request, Response, NextFunction } from 'express';
import { UserService } from './service';
import { userValidationSchemas } from './validation';
import { ClientRequest } from '../../middleware/clientDetection';

interface AuthRequest extends ClientRequest {
  user?: any;
}

export class UserController {
  static async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = userValidationSchemas.register.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { name, email, password } = req.body;

      // Email kontrolü
      const existingUser = await UserService.findUserByEmail(email);
      if (existingUser) {
        res.status(400).json({
          success: false,
          error: 'Bu email adresi zaten kullanımda'
        });
        return;
      }

      // Kullanıcı oluştur
      const user = await UserService.createUser({ name, email, password });

      // Email doğrulama gönder
      await UserService.sendVerificationEmail(user);

      // Registration successful - do NOT return token for immediate login
      // User should be redirected to login page instead
      res.status(201).json({
        success: true,
        data: {
          id: user._id,
          name: user.name,
          email: user.email,
          isEmailVerified: user.isEmailVerified,
          subscriptionEndDate: user.subscriptionEndDate
        },
        message: 'Kayıt başarılı! Email doğrulama bağlantısı gönderildi. Lütfen giriş yapın.'
      });
    } catch (error) {
      next(error);
    }
  }

  static async login(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = userValidationSchemas.login.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { email, password } = req.body;

      // Kullanıcı kontrolü
      const user = await UserService.findUserByEmail(email);
      if (!user) {
        res.status(401).json({
          success: false,
          error: 'Geçersiz email veya şifre'
        });
        return;
      }

      // Şifre kontrolü
      const isPasswordMatch = await user.matchPassword(password);
      if (!isPasswordMatch) {
        res.status(401).json({
          success: false,
          error: 'Geçersiz email veya şifre'
        });
        return;
      }

      // Access ve refresh token oluştur
      const accessToken = user.getSignedAccessToken();
      const refreshToken = user.getSignedRefreshToken();

      // Refresh token'ı veritabanına kaydet
      await user.save();

      // Client type'a göre farklı response
      if (req.clientType === 'web') {
        // Web client: refresh token'ı httpOnly cookie olarak gönder
        res.cookie('refreshToken', refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60 * 1000 // 7 gün
        });

        res.status(200).json({
          success: true,
          accessToken,
          token: accessToken, // Geriye uyumluluk için
          data: {
            id: user._id,
            name: user.name,
            email: user.email,
            isEmailVerified: user.isEmailVerified,
            subscriptionEndDate: user.subscriptionEndDate
          }
        });
      } else {
        // Mobile client: her iki token'ı da response body'de gönder
        res.status(200).json({
          success: true,
          accessToken,
          refreshToken,
          token: accessToken, // Geriye uyumluluk için
          data: {
            id: user._id,
            name: user.name,
            email: user.email,
            isEmailVerified: user.isEmailVerified,
            subscriptionEndDate: user.subscriptionEndDate
          }
        });
      }
    } catch (error) {
      next(error);
    }
  }

  static async getProfile(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user;
      
      res.status(200).json({
        success: true,
        data: {
          id: user._id,
          name: user.name,
          email: user.email,
          isEmailVerified: user.isEmailVerified,
          subscriptionEndDate: user.subscriptionEndDate,
          createdAt: user.createdAt
        }
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateProfile(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = userValidationSchemas.updateProfile.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { name, email } = req.body;
      const updateData: any = {};

      if (name) updateData.name = name;
      if (email) {
        // Email değişikliği varsa, mevcut email kontrolü
        if (email !== req.user.email) {
          const existingUser = await UserService.findUserByEmail(email);
          if (existingUser) {
            res.status(400).json({
              success: false,
              error: 'Bu email adresi zaten kullanımda'
            });
            return;
          }
          updateData.email = email;
          updateData.isEmailVerified = false; // Yeni email doğrulama gerekli
        }
      }

      const updatedUser = await UserService.updateUser(req.user._id, updateData);

      // Eğer email değiştiyse, yeni doğrulama emaili gönder
      if (email && email !== req.user.email && updatedUser) {
        await UserService.sendVerificationEmail(updatedUser);
      }

      res.status(200).json({
        success: true,
        data: {
          id: updatedUser?._id,
          name: updatedUser?.name,
          email: updatedUser?.email,
          isEmailVerified: updatedUser?.isEmailVerified,
          subscriptionEndDate: updatedUser?.subscriptionEndDate
        },
        message: email && email !== req.user.email ? 'Profil güncellendi. Yeni email doğrulama bağlantısı gönderildi.' : 'Profil güncellendi.'
      });
    } catch (error) {
      next(error);
    }
  }

  static async changePassword(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = userValidationSchemas.changePassword.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { currentPassword, newPassword } = req.body;

      const success = await UserService.changePassword(req.user, currentPassword, newPassword);
      
      if (!success) {
        res.status(400).json({
          success: false,
          error: 'Mevcut şifre yanlış'
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Şifre başarıyla değiştirildi'
      });
    } catch (error) {
      next(error);
    }
  }

  static async forgotPassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = userValidationSchemas.forgotPassword.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { email } = req.body;

      const user = await UserService.findUserByEmail(email);
      if (!user) {
        res.status(404).json({
          success: false,
          error: 'Bu email adresi ile kayıtlı kullanıcı bulunamadı'
        });
        return;
      }

      await UserService.sendPasswordResetEmail(user);

      res.status(200).json({
        success: true,
        message: 'Şifre sıfırlama bağlantısı email adresinize gönderildi'
      });
    } catch (error) {
      next(error);
    }
  }

  static async resetPassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = userValidationSchemas.resetPassword.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { token } = req.params;
      const { password } = req.body;

      const user = await UserService.resetPassword(token, password);
      if (!user) {
        res.status(400).json({
          success: false,
          error: 'Geçersiz veya süresi dolmuş token'
        });
        return;
      }

      const authToken = user.getSignedJwtToken();

      res.status(200).json({
        success: true,
        token: authToken,
        message: 'Şifre başarıyla sıfırlandı'
      });
    } catch (error) {
      next(error);
    }
  }

  static async verifyEmail(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { token } = req.params;
      console.log('Email verification request received for token:', token);

      const result = await UserService.verifyEmail(token);

      switch (result.status) {
        case 'success':
          console.log('Email verification successful, sending response');
          res.status(200).json({
            success: true,
            message: 'Email adresi başarıyla doğrulandı'
          });
          break;

        case 'already_verified':
          console.log('Email verification: User already verified');
          res.status(200).json({
            success: true,
            message: 'Email adresi zaten doğrulanmış'
          });
          break;

        case 'expired_token':
          console.log('Email verification failed: Token expired');
          res.status(400).json({
            success: false,
            error: 'Doğrulama token\'ının süresi dolmuş. Lütfen yeni bir doğrulama emaili talep edin.'
          });
          break;

        case 'invalid_token':
        default:
          console.log('Email verification failed: Invalid token');
          res.status(400).json({
            success: false,
            error: 'Geçersiz doğrulama token\'ı'
          });
          break;
      }
    } catch (error) {
      console.error('Email verification error:', error);
      next(error);
    }
  }

  static async resendVerificationEmail(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email } = req.body;

      if (!email) {
        res.status(400).json({
          success: false,
          error: 'Email adresi gereklidir'
        });
        return;
      }

      console.log('Resend verification email request for:', email);

      const result = await UserService.resendVerificationEmail(email);

      if (result.success) {
        res.status(200).json({
          success: true,
          message: result.message
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.message
        });
      }
    } catch (error) {
      console.error('Resend verification email error:', error);
      next(error);
    }
  }

  // Debug endpoint for development
  static async debugVerificationStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email } = req.params;

      if (!email) {
        res.status(400).json({
          success: false,
          error: 'Email adresi gereklidir'
        });
        return;
      }

      const user = await UserService.findUserByEmail(email);

      if (!user) {
        res.status(404).json({
          success: false,
          error: 'Kullanıcı bulunamadı'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          email: user.email,
          isEmailVerified: user.isEmailVerified,
          hasVerificationToken: !!user.emailVerificationToken,
          tokenExpiry: user.emailVerificationExpire,
          isTokenExpired: user.emailVerificationExpire ? user.emailVerificationExpire.getTime() < Date.now() : null,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      });
    } catch (error) {
      console.error('Debug verification status error:', error);
      next(error);
    }
  }

  static async getSubscriptionStatus(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const subscriptionStatus = await UserService.checkSubscriptionStatus(req.user._id);

      res.status(200).json({
        success: true,
        data: subscriptionStatus
      });
    } catch (error) {
      next(error);
    }
  }

  static async testEmailConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const isValid = await UserService.testEmailConfiguration();

      res.status(200).json({
        success: true,
        message: isValid ? 'Email configuration is valid' : 'Email configuration has issues',
        data: {
          isValid,
          emailService: process.env.EMAIL_SERVICE,
          emailUser: process.env.EMAIL_USER,
          hasEmailPass: !!process.env.EMAIL_PASS,
          emailPassLength: process.env.EMAIL_PASS?.length
        }
      });
    } catch (error) {
      next(error);
    }
  }
}