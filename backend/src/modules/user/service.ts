import { User, IUser } from './model';
import crypto from 'crypto';
import nodemailer from 'nodemailer';
import dotenv from "dotenv";

dotenv.config();

export class UserService {
  private static emailTransporter = nodemailer.createTransport({
    service: process.env.EMAIL_SERVICE || 'gmail',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    },
    // Additional configuration for better reliability
    secure: true, // Use TLS
    port: 465,
    tls: {
      rejectUnauthorized: false
    },
    // Add debug logging
    debug: process.env.NODE_ENV === 'development',
    logger: process.env.NODE_ENV === 'development'

  });

  static async createUser(userData: Partial<IUser>): Promise<IUser> {
    const user = await User.create(userData);
    
    // Kayıt tarihinden itibaren 3 günlük abonelik ver
    user.subscriptionEndDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000); // 3 gün
    await user.save();

    return user as IUser;
  }

  static async findUserByEmail(email: string): Promise<IUser | null> {
    return await User.findOne({ email }).select('+password') as IUser | null;
  }

  static async findUserById(id: string): Promise<IUser | null> {
    return await User.findById(id) as IUser | null;
  }

  static async updateUser(id: string, updateData: Partial<IUser>): Promise<IUser | null> {
    return await User.findByIdAndUpdate(id, updateData, {
      new: true,
      runValidators: true
    }) as IUser | null;
  }

  static async deleteUser(id: string): Promise<void> {
    await User.findByIdAndDelete(id);
  }

  static async sendVerificationEmail(user: IUser): Promise<void> {
    try {
      const verificationToken = user.getEmailVerificationToken();
      await user.save();

      const verificationUrl = `${process.env.FRONTEND_URL}/verify-email/${verificationToken}`;

      const message = `
        <h1>Email Doğrulama</h1>
        <p>Merhaba ${user.name},</p>
        <p>Email adresinizi doğrulamak için aşağıdaki bağlantıya tıklayın:</p>
        <a href="${verificationUrl}" target="_blank">Email Adresimi Doğrula</a>
        <p>Bu bağlantı 24 saat geçerlidir.</p>
      `;

      // Verify transporter configuration before sending
      await this.emailTransporter.verify();

      await this.emailTransporter.sendMail({
        from: process.env.EMAIL_USER,
        to: user.email,
        subject: 'Email Doğrulama - PROFORMA',
        html: message
      });

      console.log(`Verification email sent successfully to ${user.email}`);
    } catch (error) {
      console.error('Email sending error:', error);
      // Don't throw the error to prevent registration failure
      // Log the error but allow registration to continue
      console.log('Registration will continue without email verification');
    }
  }

  static async resendVerificationEmail(email: string): Promise<{ success: boolean; message: string }> {
    try {
      const user = await User.findOne({ email }) as IUser | null;

      if (!user) {
        return { success: false, message: 'Kullanıcı bulunamadı' };
      }

      if (user.isEmailVerified) {
        return { success: false, message: 'Email adresi zaten doğrulanmış' };
      }

      // Generate new verification token
      const verificationToken = user.getEmailVerificationToken();
      await user.save();

      const verificationUrl = `${process.env.FRONTEND_URL}/verify-email/${verificationToken}`;

      const message = `
        <h1>Email Doğrulama - Yeniden Gönderim</h1>
        <p>Merhaba ${user.name},</p>
        <p>Email doğrulama talebiniz üzerine yeni bir doğrulama bağlantısı gönderiyoruz.</p>
        <p>Email adresinizi doğrulamak için aşağıdaki bağlantıya tıklayın:</p>
        <a href="${verificationUrl}" target="_blank">Email Adresimi Doğrula</a>
        <p>Bu bağlantı 24 saat geçerlidir.</p>
        <p>Önceki doğrulama bağlantıları artık geçersizdir.</p>
      `;

      await this.emailTransporter.verify();

      await this.emailTransporter.sendMail({
        from: process.env.EMAIL_USER,
        to: user.email,
        subject: 'Email Doğrulama - Yeniden Gönderim - PROFORMA',
        html: message
      });

      console.log(`Verification email resent successfully to ${user.email}`);
      return { success: true, message: 'Doğrulama emaili yeniden gönderildi' };
    } catch (error) {
      console.error('Resend verification email error:', error);
      return { success: false, message: 'Email gönderilirken hata oluştu' };
    }
  }

  static async verifyEmail(token: string): Promise<{ user: IUser | null; status: 'success' | 'already_verified' | 'invalid_token' | 'expired_token' }> {
    console.log('Verifying email with token:', token);
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
    console.log('Hashed token:', hashedToken);

    // First, check if there's a user with this token (regardless of expiration)
    const userWithToken = await User.findOne({
      emailVerificationToken: hashedToken
    }) as IUser | null;

    // If no user found with this token, check if there's a user who might have already been verified
    if (!userWithToken) {
      console.log('No user found with this token. Checking if token was already used...');

      // Try to find any user who might have used this token before (this is a fallback check)
      // Since we clear the token after verification, we can't directly match it
      // But we can check recent verifications by looking for recently verified users
      const recentlyVerifiedUsers = await User.find({
        isEmailVerified: true,
        emailVerificationToken: { $exists: false },
        updatedAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
      }) as IUser[];

      console.log(`Found ${recentlyVerifiedUsers.length} recently verified users`);

      // If we have recently verified users, it's likely the token was already used
      if (recentlyVerifiedUsers.length > 0) {
        console.log('Email verification failed: Token likely already used (user already verified)');
        return { user: null, status: 'already_verified' };
      }

      console.log('Email verification failed: Invalid token');
      return { user: null, status: 'invalid_token' };
    }

    // Check if the token has expired
    if (userWithToken.emailVerificationExpire && userWithToken.emailVerificationExpire.getTime() < Date.now()) {
      console.log('Email verification failed: Token expired');
      return { user: null, status: 'expired_token' };
    }

    // Check if user is already verified
    if (userWithToken.isEmailVerified) {
      console.log(`User ${userWithToken.email} is already verified, but token still exists. Cleaning up...`);

      // Clean up the token since user is already verified
      userWithToken.emailVerificationToken = undefined;
      userWithToken.emailVerificationExpire = undefined;
      await userWithToken.save();

      return { user: userWithToken as IUser, status: 'already_verified' };
    }

    console.log('User found for verification:', `${userWithToken.email} (ID: ${userWithToken._id})`);

    // Verify the user
    userWithToken.isEmailVerified = true;
    userWithToken.emailVerificationToken = undefined;
    userWithToken.emailVerificationExpire = undefined;
    await userWithToken.save();

    console.log(`Email verification successful for user: ${userWithToken.email}`);
    return { user: userWithToken as IUser, status: 'success' };
  }

  static async sendPasswordResetEmail(user: IUser): Promise<void> {
    try {
      const resetToken = user.getResetPasswordToken();
      await user.save();

      const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;

      const message = `
        <h1>Şifre Sıfırlama</h1>
        <p>Merhaba ${user.name},</p>
        <p>Şifrenizi sıfırlamak için aşağıdaki bağlantıya tıklayın:</p>
        <a href="${resetUrl}" target="_blank">Şifremi Sıfırla</a>
        <p>Bu bağlantı 10 dakika geçerlidir.</p>
        <p>Eğer bu isteği siz yapmadıysanız, bu emaili görmezden gelebilirsiniz.</p>
      `;

      // Verify transporter configuration before sending
      await this.emailTransporter.verify();

      await this.emailTransporter.sendMail({
        from: process.env.EMAIL_USER,
        to: user.email,
        subject: 'Şifre Sıfırlama - PROFORMA',
        html: message
      });

      console.log(`Password reset email sent successfully to ${user.email}`);
    } catch (error) {
      console.error('Password reset email sending error:', error);
      // For password reset, we should throw the error since it's critical
      throw new Error('Email gönderilirken hata oluştu. Lütfen daha sonra tekrar deneyin.');
    }
  }

  static async resetPassword(token: string, newPassword: string): Promise<IUser | null> {
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

    const user = await User.findOne({
      resetPasswordToken: hashedToken,
      resetPasswordExpire: { $gt: Date.now() }
    }) as IUser | null;

    if (!user) {
      return null;
    }

    user.password = newPassword;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;
    await user.save();

    return user as IUser;
  }

  static async changePassword(user: IUser, currentPassword: string, newPassword: string): Promise<boolean> {
    const isCurrentPasswordCorrect = await user.matchPassword(currentPassword);
    
    if (!isCurrentPasswordCorrect) {
      return false;
    }

    user.password = newPassword;
    await user.save();
    return true;
  }

  static async extendSubscription(userId: string, days: number): Promise<IUser | null> {
    const user = await User.findById(userId) as IUser | null;
    if (!user) return null;

    const currentEndDate = user.subscriptionEndDate || new Date();
    const newEndDate = new Date(currentEndDate.getTime() + days * 24 * 60 * 60 * 1000);

    user.subscriptionEndDate = newEndDate;
    await user.save();

    return user as IUser;
  }

  // Test email configuration
  static async testEmailConfiguration(): Promise<boolean> {
    try {


      await this.emailTransporter.verify();

      return true;
    } catch (error) {
      console.error('❌ Email configuration error:', error);
      console.error('Error details:', {
        code: (error as any).code,
        command: (error as any).command,
        message: (error as any).message
      });
      return false;
    }
  }

  static async checkSubscriptionStatus(userId: string): Promise<{ isActive: boolean; daysLeft: number; endDate: Date | null }> {
    const user = await User.findById(userId) as IUser | null;
    if (!user || !user.subscriptionEndDate) {
      return { isActive: false, daysLeft: 0, endDate: null };
    }

    const now = new Date();
    const endDate = user.subscriptionEndDate;
    const isActive = now < endDate;
    const daysLeft = isActive ? Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : 0;

    return { isActive, daysLeft, endDate };
  }
} 