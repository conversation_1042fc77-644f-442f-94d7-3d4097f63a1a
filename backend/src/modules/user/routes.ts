import { Router } from 'express';
import { UserController } from './controller';
import { protect } from '../../middleware/auth';

const router = Router();

// Profil routes
router.get('/profile', protect, UserController.getProfile);
router.put('/profile', protect, UserController.updateProfile);
router.put('/change-password', protect, UserController.changePassword);

// Abonelik durumu
router.get('/subscription-status', protect, UserController.getSubscriptionStatus);

// Development only routes
if (process.env.NODE_ENV === 'development') {
  router.get('/test-email-config', UserController.testEmailConfig);
  router.get('/debug-verification/:email', UserController.debugVerificationStatus);
}

export default router;