import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IUser extends Document {
  name: string;
  email: string;
  password: string;
  isEmailVerified: boolean;
  subscriptionEndDate?: Date;
  resetPasswordToken?: string;
  resetPasswordExpire?: Date;
  emailVerificationToken?: string;
  emailVerificationExpire?: Date;
  refreshToken?: string;
  refreshTokenExpire?: Date;
  tokenVersion: number;
  createdAt: Date;
  updatedAt: Date;
  matchPassword(enteredPassword: string): Promise<boolean>;
  getSignedJwtToken(): string;
  getSignedAccessToken(): string;
  getSignedRefreshToken(): string;
  getResetPasswordToken(): string;
  getEmailVerificationToken(): string;
  validateRefreshToken(token: string): boolean;
  invalidateAllTokens(): void;
}

const userSchema: Schema = new Schema({
  name: {
    type: String,
    required: [true, '<PERSON>sim alanı zorunludur'],
    trim: true,
    maxlength: [50, 'İsim 50 karakterden fazla olamaz']
  },
  email: {
    type: String,
    required: [true, 'Email alanı zorunludur'],
    unique: true,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Geçerli bir email adresi giriniz'
    ]
  },
  password: {
    type: String,
    required: [true, 'Şifre alanı zorunludur'],
    minlength: [6, 'Şifre en az 6 karakter olmalıdır'],
    select: false
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  subscriptionEndDate: {
    type: Date,
    default: null
  },
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  emailVerificationToken: String,
  emailVerificationExpire: Date,
  refreshToken: String,
  refreshTokenExpire: Date,
  tokenVersion: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Şifreyi kaydetmeden önce şifrele
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    return next();
  }

  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
  next();
});

// Şifre karşılaştırma metodu
userSchema.methods.matchPassword = async function(enteredPassword: string): Promise<boolean> {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Access token oluşturma (kısa süreli)
userSchema.methods.getSignedAccessToken = function(): string {
  const jwt = require('jsonwebtoken');
  const crypto = require('crypto');

  return jwt.sign(
    {
      id: this._id,
      tokenVersion: this.tokenVersion,
      type: 'access',
      jti: crypto.randomBytes(16).toString('hex') // Unique token ID for blacklisting
    },
    process.env.JWT_SECRET,
    {
      expiresIn: process.env.JWT_ACCESS_EXPIRE || '15m'
    }
  );
};

// Refresh token oluşturma (uzun süreli)
userSchema.methods.getSignedRefreshToken = function(): string {
  const jwt = require('jsonwebtoken');
  const crypto = require('crypto');

  // Refresh token için ayrı bir secret kullan
  const refreshSecret = process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET + '_refresh';

  const refreshToken = jwt.sign(
    {
      id: this._id,
      tokenVersion: this.tokenVersion,
      type: 'refresh',
      jti: crypto.randomBytes(16).toString('hex') // Unique token ID
    },
    refreshSecret,
    {
      expiresIn: process.env.JWT_REFRESH_EXPIRE || '7d'
    }
  );

  // Refresh token'ı veritabanında sakla
  this.refreshToken = crypto.createHash('sha256').update(refreshToken).digest('hex');
  this.refreshTokenExpire = new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)); // 7 gün

  return refreshToken;
};

// Eski JWT token metodu (geriye uyumluluk için)
userSchema.methods.getSignedJwtToken = function(): string {
  return this.getSignedAccessToken();
};

// Reset password token oluşturma
userSchema.methods.getResetPasswordToken = function(): string {
  const crypto = require('crypto');
  const resetToken = crypto.randomBytes(20).toString('hex');

  this.resetPasswordToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  this.resetPasswordExpire = Date.now() + 10 * 60 * 1000; // 10 dakika

  return resetToken;
};

// Email verification token oluşturma
userSchema.methods.getEmailVerificationToken = function(): string {
  const crypto = require('crypto');
  const verificationToken = crypto.randomBytes(20).toString('hex');

  this.emailVerificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');

  this.emailVerificationExpire = Date.now() + 24 * 60 * 60 * 1000; // 24 saat

  return verificationToken;
};

// Refresh token doğrulama
userSchema.methods.validateRefreshToken = function(token: string): boolean {
  const crypto = require('crypto');
  const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

  return this.refreshToken === hashedToken &&
         this.refreshTokenExpire &&
         this.refreshTokenExpire > new Date();
};

// Token version'ı artır (tüm token'ları geçersiz kıl)
userSchema.methods.invalidateAllTokens = function(): void {
  this.tokenVersion += 1;
  this.refreshToken = undefined;
  this.refreshTokenExpire = undefined;
};

export const User = mongoose.model<IUser>('User', userSchema);