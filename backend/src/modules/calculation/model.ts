import mongoose, { Document, Schema } from 'mongoose';

// Vessel Types
export type VesselType = 'passenger' | 'ro-ro-ro-pax' | 'car-carrier' | 'container' | 'lng-tanker' | 'bunker-tanker' | 'other';

// Flag Categories
export type FlagCategory = 'cabotage' | 'turkish' | 'foreign';

// Cargo Types
export type CargoType = 'general' | 'dangerous';

// Port Call Types
export type PortCallType = 'simple-port-call' | 'bosphorus-transit' | 'canakkale-transit' | 'transit-with-port-call';

// Light Due Operation Types for Turkish Flag vessels
export type LightDueOperationType =
  | 'round-trip-without-port-call'  // Uğrakısız Sefer (Gidiş-Dönüş)
  | 'dardanelles-entry'             // Çanakkale Boğazı Giriş
  | 'dardanelles-exit'              // Çanakkale Boğazı Çıkış
  | 'bosphorus-entry'               // İstanbul Boğazı Giriş
  | 'bosphorus-exit'                // İstanbul Boğazı Çıkış
  | 'port-entry'                    // Liman Giriş
  | 'port-exit'                     // Liman Çıkış
  | 'port-entry-exit'               // Liman Giriş + Çıkış (combined)
  | 'standard';                     // Standard calculation for non-Turkish flags

// Legacy interfaces for general calculations
export interface ICalculationInput {
  maliyetGrubu: string;
  miktar: number;
  birimFiyat: number;
  kdvOrani: number;
  iskontoOrani?: number;
  ozelDurum?: string;
  aciklama?: string;
}

export interface ICalculationResult {
  toplamMaliyet: number;
  kdvTutari: number;
  iskontoTutari: number;
  netTutar: number;
  detayliHesaplama: any;
}

export interface IMaritimeCalculationInput {
  // Basic vessel information
  portName: string;
  portLocation: string;
  portCallType: PortCallType;
  vesselType: VesselType;
  flagCategory: FlagCategory;
  
  // Vessel specifications
  grossRegisterTonnage: number; // GRT
  netRegisterTonnage: number;   // NRT
  deadweightTonnage?: number;   // DWT
  
  // Port stay information
  daysAtQuay: number;
  cargoType: CargoType;
  
  // Cargo information
  cargoQuantity?: number;
  cargoUnits?: number;        // TEU count for containers, vehicle count for RoRo
  cargoNature?: string;
  cargoCategory?: string; // bulk-solid, bulk-liquid, containers, vehicles, etc.
  
  // Exchange rates
  usdTryRate: number;
  eurUsdRate?: number;
  
  // Special conditions
  specialConditions?: string[];
  isIzmitKorfezi?: boolean;
  isDangerous?: boolean;
}

export interface IFeeBreakdown {
  pilotage: {
    baseAmount: number;
    numberOfPilots: number;
    dangerousSurcharge?: number;
    turkishFlagDiscount?: number;
    total: number;
    calculation: string;
  };
  tugboat: {
    numberOfTugs: number;
    ratePerTug: number;
    baseAmount: number;
    dangerousSurcharge?: number;
    turkishFlagDiscount?: number;
    total: number;
    calculation: string;
  };
  mooring: {
    berthingFee: number;
    unberthingFee: number;
    baseAmount: number;
    dangerousSurcharge?: number;
    turkishFlagDiscount?: number;
    total: number;
    calculation: string;
  };
  quayDue: {
    dailyRate: number;
    days: number;
    baseAmount: number;
    dangerousSurcharge?: number;
    total: number;
    calculation: string;
  };
  garbageCollection: {
    amount: number;
    currency: 'EUR' | 'USD';
    usdAmount: number;
    calculation: string;
  };
  sanitaryFee: {
    amount: number;
    baseAmountTL: number;
    usdAmount: number;
    calculation: string;
  };
  lightDue: {
    first800NRT: number;
    above800NRT: number;
    operationType?: LightDueOperationType;
    multiplierUsed?: number;
    total: number;
    calculation: string;
  };
  vesselTrafficFee?: {
    entry: number;
    departure: number;
    total: number;
    nrtRange?: string;
    flagCategory?: string;
    calculation?: string;
    applicablePorts?: string[];
  };
  harbourMasterFee: {
    baseAmountTL: number;
    usdAmount: number;
    calculation: string;
  };
  chamberOfShipping: {
    amount: number;
    baseAmountTL?: number;
    currency: 'EUR' | 'USD' | 'TL';
    usdAmount: number;
    calculation: string;
  };
  freightShare?: {
    amount: number;
    currency: 'EUR' | 'USD';
    usdAmount: number;
    calculation: string;
  };
  agencyFee: {
    amount: number;
    currency: 'EUR' | 'USD' | 'TL';
    usdAmount: number;
    calculation: string;
  };
  attendanceSupervisionFee: {
    cargoCategory: string;
    tonnageOrUnits: number;
    ratePerUnit: number;
    baseAmount: number;
    minAmount: number;
    maxAmount: number;
    finalAmount: number;
    currency: 'EUR' | 'USD';
    usdAmount: number;
    calculation: string;
  };
  containerDangerousCargoFee?: {
    teuCount: number;
    ratePerTEU: number;
    totalAmount: number;
    currency: 'EUR' | 'USD';
    usdAmount: number;
    calculation: string;
  };
  transitFees?: {
    transitPilotage: number;
    transitTugboat?: number;
    transitSanitary: number;
    transitLight: number;
    transitAgency: number;
    total: number;
  };
}

export interface IMaritimeCalculationResult {
  input: IMaritimeCalculationInput;
  roundedGRT: number;
  roundedNRT: number;
  fees: IFeeBreakdown;
  subtotals: {
    portCallServices: number;
    transitServices?: number;
  };
  dangerousCargoSurcharge: boolean;
  grandTotal: number;
  currency: 'USD';
  calculationNotes: string[];
  errors?: string[];
}

export interface ICalculation extends Document {
  userId: mongoose.Types.ObjectId;
  calculationType: 'maritime-proforma' | 'general-calculation';
  
  // For maritime calculations
  maritimeInput?: IMaritimeCalculationInput;
  maritimeResult?: IMaritimeCalculationResult;
  
  // For general calculations (legacy)
  inputData?: any[];
  result?: any;
  
  calculationDate: Date;
  title?: string;
  description?: string;
  isPublic: boolean;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

const maritimeInputSchema = new Schema({
  portName: { type: String, required: true },
  portLocation: { type: String, required: true },
  portCallType: {
    type: String,
    enum: ['simple-port-call', 'bosphorus-transit', 'canakkale-transit', 'transit-with-port-call'],
    required: true
  },
  vesselType: {
    type: String,
    enum: ['passenger', 'ro-ro-ro-pax', 'car-carrier', 'container', 'lng-tanker', 'bunker-tanker', 'other'],
    required: true
  },
  flagCategory: {
    type: String,
    enum: ['cabotage', 'turkish', 'foreign'],
    required: true
  },
  grossRegisterTonnage: { type: Number, required: true, min: 1 },
  netRegisterTonnage: { type: Number, required: true, min: 1 },
  deadweightTonnage: { type: Number, min: 1 },
  daysAtQuay: { type: Number, required: true, min: 1 },
  cargoType: {
    type: String,
    enum: ['general', 'dangerous'],
    required: true
  },
  cargoQuantity: { type: Number, min: 0 },
  cargoUnits: { type: Number, min: 0 },
  cargoNature: String,
  cargoCategory: String,
  usdTryRate: { type: Number, required: true, min: 0.01 },
  eurUsdRate: { type: Number, min: 0.01 },
  specialConditions: [String],
  isIzmitKorfezi: { type: Boolean, default: false },
  isDangerous: { type: Boolean, default: false }
}, { _id: false });

const feeBreakdownSchema = new Schema({
  pilotage: { type: Schema.Types.Mixed },
  tugboat: { type: Schema.Types.Mixed },
  mooring: { type: Schema.Types.Mixed },
  quayDue: { type: Schema.Types.Mixed },
  garbageCollection: { type: Schema.Types.Mixed },
  sanitaryFee: { type: Schema.Types.Mixed },
  lightDue: { type: Schema.Types.Mixed },
  vesselTrafficFee: { type: Schema.Types.Mixed },
  harbourMasterFee: { type: Schema.Types.Mixed },
  chamberOfShipping: { type: Schema.Types.Mixed },
  freightShare: { type: Schema.Types.Mixed },
  agencyFee: { type: Schema.Types.Mixed },
  attendanceSupervisionFee: { type: Schema.Types.Mixed },
  transitFees: { type: Schema.Types.Mixed }
}, { _id: false });

const maritimeResultSchema = new Schema({
  input: maritimeInputSchema,
  roundedGRT: { type: Number, required: true },
  roundedNRT: { type: Number, required: true },
  fees: feeBreakdownSchema,
  subtotals: { type: Schema.Types.Mixed },
  dangerousCargoSurcharge: { type: Boolean, required: true },
  grandTotal: { type: Number, required: true },
  currency: { type: String, enum: ['USD'], default: 'USD' },
  calculationNotes: [String],
  errors: [String]
}, { _id: false });

const calculationSchema: Schema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  calculationType: {
    type: String,
    enum: ['maritime-proforma', 'general-calculation'],
    required: true,
    default: 'maritime-proforma'
  },
  
  // Maritime calculation fields
  maritimeInput: maritimeInputSchema,
  maritimeResult: maritimeResultSchema,
  
  // Legacy fields for general calculations
  inputData: [Schema.Types.Mixed],
  result: Schema.Types.Mixed,
  
  calculationDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  title: {
    type: String,
    maxlength: 200,
    trim: true
  },
  description: {
    type: String,
    maxlength: 1000,
    trim: true
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    maxlength: 50,
    trim: true
  }]
}, {
  timestamps: true
});

// Indexes
calculationSchema.index({ userId: 1 });
calculationSchema.index({ calculationType: 1 });
calculationSchema.index({ calculationDate: -1 });
calculationSchema.index({ isPublic: 1 });
calculationSchema.index({ tags: 1 });

// Virtual fields
calculationSchema.virtual('formattedDate').get(function(this: ICalculation) {
  return this.calculationDate.toLocaleDateString('tr-TR');
});

// Methods
calculationSchema.methods.toSummary = function(this: ICalculation) {
  const total = this.calculationType === 'maritime-proforma' 
    ? this.maritimeResult?.grandTotal 
    : this.result?.netTutar;
    
  return {
    id: this._id,
    title: this.title || `${this.calculationType} Hesaplama`,
    calculationType: this.calculationType,
    total: total,
    calculationDate: this.calculationDate,
    portName: this.maritimeInput?.portName
  };
};

export const Calculation = mongoose.model<ICalculation>('Calculation', calculationSchema); 