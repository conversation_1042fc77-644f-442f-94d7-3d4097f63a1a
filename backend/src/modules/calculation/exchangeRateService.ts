import axios from 'axios';

interface TCMBExchangeRate {
  Tarih: string;
  USD: {
    ForexBuying: string;
    ForexSelling: string;
    BanknoteBuying: string;
    BanknoteSelling: string;
  };
  EUR: {
    ForexBuying: string;
    ForexSelling: string;
    BanknoteBuying: string;
    BanknoteSelling: string;
  };
}

export class ExchangeRateService {
  private static readonly TCMB_URL = 'https://www.tcmb.gov.tr/kurlar/today.xml';
  private static readonly BACKUP_URL = 'https://api.exchangerate-api.com/v4/latest/USD';
  
  /**
   * TCMB'den güncel USD/TRY ve EUR/USD kurlarını çeker
   */
  static async getCurrentRates(): Promise<{
    usdTry: number;
    eurUsd: number;
    date: string;
    source: 'TCMB' | 'BACKUP';
  }> {
    try {
      // Önce TCMB'den çekmeyi dene
      const tcmbRates = await this.fetchFromTCMB();
      if (tcmbRates) {
        return tcmbRates;
      }
    } catch (error) {
      console.warn('TCMB kurları alınamadı, yedek servise geçiliyor:', error);
    }

    try {
      // Yedek servisten çek
      const backupRates = await this.fetchFromBackupService();
      return backupRates;
    } catch (error) {
      console.error('Hiçbir servisten kur alınamadı:', error);
      throw new Error('Exchange rates could not be fetched from any source');
    }
  }

  private static async fetchFromTCMB(): Promise<{
    usdTry: number;
    eurUsd: number;
    date: string;
    source: 'TCMB';
  } | null> {
    try {
      const response = await axios.get(this.TCMB_URL, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      // XML'i parse et
      const xml = response.data;
      
      // USD/TRY kurunu çıkar
      const usdMatch = xml.match(/<Currency[^>]*CurrencyCode="USD"[^>]*>(.*?)<\/Currency>/s);
      const eurMatch = xml.match(/<Currency[^>]*CurrencyCode="EUR"[^>]*>(.*?)<\/Currency>/s);
      
      if (!usdMatch || !eurMatch) {
        throw new Error('USD or EUR not found in TCMB response');
      }

      // USD alış kuru
      const usdForexBuyingMatch = usdMatch[1].match(/<ForexBuying>([\d.]+)<\/ForexBuying>/);
      const eurForexBuyingMatch = eurMatch[1].match(/<ForexBuying>([\d.]+)<\/ForexBuying>/);
      
      if (!usdForexBuyingMatch || !eurForexBuyingMatch) {
        throw new Error('ForexBuying rates not found');
      }

      const usdTry = parseFloat(usdForexBuyingMatch[1]);
      const eurTry = parseFloat(eurForexBuyingMatch[1]);
      const eurUsd = eurTry / usdTry; // EUR/USD = EUR/TRY ÷ USD/TRY

      // Tarihi çıkar
      const dateMatch = xml.match(/Date="([^"]+)"/);
      const date = dateMatch ? dateMatch[1] : new Date().toISOString().split('T')[0];

      return {
        usdTry,
        eurUsd,
        date,
        source: 'TCMB'
      };
    } catch (error) {
      console.error('TCMB fetch error:', error);
      return null;
    }
  }

  private static async fetchFromBackupService(): Promise<{
    usdTry: number;
    eurUsd: number;
    date: string;
    source: 'BACKUP';
  }> {
    const response = await axios.get(this.BACKUP_URL, {
      timeout: 10000
    });

    const data = response.data;
    
    if (!data.rates || !data.rates.TRY || !data.rates.EUR) {
      throw new Error('Invalid response from backup service');
    }

    const usdTry = data.rates.TRY;
    const eurUsd = 1 / data.rates.EUR; // EUR/USD = 1 ÷ USD/EUR

    return {
      usdTry,
      eurUsd,
      date: data.date || new Date().toISOString().split('T')[0],
      source: 'BACKUP'
    };
  }

  /**
   * Belirli bir tarih için TCMB kurlarını çeker
   */
  static async getRatesForDate(date: string): Promise<{
    usdTry: number;
    eurUsd: number;
    date: string;
  } | null> {
    try {
      // Tarihi DDMMYYYY formatına çevir
      const [year, month, day] = date.split('-');
      const formattedDate = `${day}${month}${year}`;
      
      const url = `https://www.tcmb.gov.tr/kurlar/${year}${month}/${formattedDate}.xml`;
      
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const xml = response.data;
      
      // USD/TRY kurunu çıkar
      const usdMatch = xml.match(/<Currency[^>]*CurrencyCode="USD"[^>]*>(.*?)<\/Currency>/s);
      const eurMatch = xml.match(/<Currency[^>]*CurrencyCode="EUR"[^>]*>(.*?)<\/Currency>/s);
      
      if (!usdMatch || !eurMatch) {
        return null;
      }

      const usdForexBuyingMatch = usdMatch[1].match(/<ForexBuying>([\d.]+)<\/ForexBuying>/);
      const eurForexBuyingMatch = eurMatch[1].match(/<ForexBuying>([\d.]+)<\/ForexBuying>/);
      
      if (!usdForexBuyingMatch || !eurForexBuyingMatch) {
        return null;
      }

      const usdTry = parseFloat(usdForexBuyingMatch[1]);
      const eurTry = parseFloat(eurForexBuyingMatch[1]);
      const eurUsd = eurTry / usdTry;

      return {
        usdTry,
        eurUsd,
        date
      };
    } catch (error) {
      console.error(`Error fetching rates for date ${date}:`, error);
      return null;
    }
  }

  /**
   * Kur geçmişini kontrol eder ve önbellek yönetimi yapar
   */
  static async getRatesWithCache(): Promise<{
    usdTry: number;
    eurUsd: number;
    date: string;
    source: 'TCMB' | 'BACKUP' | 'CACHE';
  }> {
    // Basit önbellek - production'da Redis kullanılabilir
    const cacheKey = `exchange_rates_${new Date().toISOString().split('T')[0]}`;
    
    // Bu örnekte sadece memory cache kullanıyoruz
    // Production'da Redis veya başka bir cache sistemi kullanılmalı
    
    try {
      const rates = await this.getCurrentRates();
      return rates;
    } catch (error) {
      // Son çare olarak sabit kurlar
      console.error('All exchange rate sources failed, using fallback rates');
      return {
        usdTry: 30.0, // Fallback rate
        eurUsd: 1.1,  // Fallback rate
        date: new Date().toISOString().split('T')[0],
        source: 'BACKUP'
      };
    }
  }
} 