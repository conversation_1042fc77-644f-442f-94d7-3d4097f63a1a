import { Request, Response, NextFunction } from 'express';
import { CalculationService } from './service';
import { calculationValidationSchemas, validateMaritimeCalculationInput } from './validation';
import { IMaritimeCalculationInput } from './model';
import { ExchangeRateService } from './exchangeRateService';
import { PDFService } from '../../services/pdfService';

interface AuthRequest extends Request {
  user?: any;
}

export class CalculationController {
  private calculationService: CalculationService;

  constructor() {
    this.calculationService = new CalculationService();
  }

  static async createCalculation(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = calculationValidationSchemas.createCalculation.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const userId = req.user._id;
      const calculation = await CalculationService.createCalculation(userId, req.body);

      res.status(201).json({
        success: true,
        data: calculation,
        message: '<PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>ı<PERSON> oluşturuldu'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getCalculation(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?._id;

      const calculation = await CalculationService.getCalculationById(id, userId);

      if (!calculation) {
        res.status(404).json({
          success: false,
          error: 'Hesaplama bulunamadı'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: calculation
      });
    } catch (error) {
      next(error);
    }
  }

  static async getUserCalculations(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = calculationValidationSchemas.getCalculations.validate(req.query);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const userId = req.user._id;
      const filters = req.query;

      const result = await CalculationService.getUserCalculations(userId, filters);

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateCalculation(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = calculationValidationSchemas.updateCalculation.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { id } = req.params;
      const userId = req.user._id;

      const calculation = await CalculationService.updateCalculation(id, userId, req.body);

      if (!calculation) {
        res.status(404).json({
          success: false,
          error: 'Hesaplama bulunamadı veya güncellenecek izniniz yok'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: calculation,
        message: 'Hesaplama başarıyla güncellendi'
      });
    } catch (error) {
      next(error);
    }
  }

  static async deleteCalculation(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user._id;

      const success = await CalculationService.deleteCalculation(id, userId);

      if (!success) {
        res.status(404).json({
          success: false,
          error: 'Hesaplama bulunamadı veya silme izniniz yok'
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Hesaplama başarıyla silindi'
      });
    } catch (error) {
      next(error);
    }
  }

  static async duplicateCalculation(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user._id;

      const duplicatedCalculation = await CalculationService.duplicateCalculation(id, userId);

      if (!duplicatedCalculation) {
        res.status(404).json({
          success: false,
          error: 'Kopyalanacak hesaplama bulunamadı'
        });
        return;
      }

      res.status(201).json({
        success: true,
        data: duplicatedCalculation,
        message: 'Hesaplama başarıyla kopyalandı'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getCalculationStats(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user._id;
      const stats = await CalculationService.getCalculationStats(userId);

      res.status(200).json({
        success: true,
        data: stats
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPublicCalculations(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = calculationValidationSchemas.getCalculations.validate(req.query);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const filters = req.query;
      const result = await CalculationService.getPublicCalculations(filters);

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // Alias for getUserCalculations (for legacy routes)
  static getCalculations = CalculationController.getUserCalculations;

  // Alias for getCalculation (for legacy routes)  
  static getCalculationById = CalculationController.getCalculation;

  // Calculate maritime proforma
  public calculateMaritimeProforma = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ error: 'Kullanıcı kimlik doğrulaması gerekli' });
        return;
      }

      // Validate input
      const { error, value } = validateMaritimeCalculationInput(req.body);
      if (error) {
        res.status(400).json({ 
          error: 'Geçersiz giriş verileri', 
          details: error.details.map(d => d.message) 
        });
        return;
      }

      const input: IMaritimeCalculationInput = value;

      // Calculate
      const result = await this.calculationService.calculateMaritimeProforma(userId, input);

      res.status(200).json({
        success: true,
        data: result
      });

    } catch (error: any) {
      console.error('Calculate maritime proforma error:', error);
      res.status(500).json({ 
        error: 'Hesaplama sırasında hata oluştu',
        details: error.message 
      });
    }
  };



  // Save calculation
  public saveCalculation = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ error: 'Kullanıcı kimlik doğrulaması gerekli' });
        return;
      }

      const { result, title, description } = req.body;

      if (!result) {
        res.status(400).json({ error: 'Hesaplama sonucu gerekli' });
        return;
      }

      const calculation = await this.calculationService.saveCalculation(
        userId,
        result,
        title,
        description
      );

      res.status(201).json({
        success: true,
        data: calculation
      });

    } catch (error: any) {
      console.error('Save calculation error:', error);
      res.status(500).json({ 
        error: 'Hesaplama kaydedilirken hata oluştu',
        details: error.message 
      });
    }
  };

  // Get user calculations
  public getUserCalculations = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ error: 'Kullanıcı kimlik doğrulaması gerekli' });
        return;
      }

      const skip = parseInt(req.query.skip as string) || 0;
      const limit = parseInt(req.query.limit as string) || 10;

      const { calculations, total } = await this.calculationService.getUserCalculations(
        userId,
        skip,
        limit
      );

      res.status(200).json({
        success: true,
        data: {
          calculations,
          total,
          skip,
          limit
        }
      });

    } catch (error: any) {
      console.error('Get user calculations error:', error);
      res.status(500).json({ 
        error: 'Hesaplamalar getirilirken hata oluştu',
        details: error.message 
      });
    }
  };

  // Get calculation by ID
  public getCalculationById = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ error: 'Kullanıcı kimlik doğrulaması gerekli' });
        return;
      }

      const { id } = req.params;

      if (!id) {
        res.status(400).json({ error: 'Hesaplama ID gerekli' });
        return;
      }

      const calculation = await this.calculationService.getCalculationById(id, userId);

      if (!calculation) {
        res.status(404).json({ error: 'Hesaplama bulunamadı' });
        return;
      }

      res.status(200).json({
        success: true,
        data: calculation
      });

    } catch (error: any) {
      console.error('Get calculation by ID error:', error);
      res.status(500).json({ 
        error: 'Hesaplama getirilirken hata oluştu',
        details: error.message 
      });
    }
  };

  // Delete calculation
  public deleteCalculation = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ error: 'Kullanıcı kimlik doğrulaması gerekli' });
        return;
      }

      const { id } = req.params;

      if (!id) {
        res.status(400).json({ error: 'Hesaplama ID gerekli' });
        return;
      }

      const deleted = await this.calculationService.deleteCalculation(id, userId);

      if (!deleted) {
        res.status(404).json({ error: 'Hesaplama bulunamadı' });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Hesaplama başarıyla silindi'
      });

    } catch (error: any) {
      console.error('Delete calculation error:', error);
      res.status(500).json({ 
        error: 'Hesaplama silinirken hata oluştu',
        details: error.message 
      });
    }
  };

  // Get port suggestions (for autocomplete)
  public getPortSuggestions = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const { query } = req.query;

      // Turkish ports organized by province
      const ports = [
        // İzmir (Aliağa & Dikili)
        { name: 'İzmir Demir Çelik (İDC)', location: 'Izmir' },
        { name: 'Batıliman', location: 'Izmir' },
        { name: 'Nemport', location: 'Izmir' },
        { name: 'Ege Gübre', location: 'Izmir' },
        { name: 'SOCAR', location: 'Izmir' },
        { name: 'Tüpraş', location: 'Izmir' },
        { name: 'STAR Rafineri', location: 'Izmir' },
        { name: 'Petkim', location: 'Izmir' },
        { name: 'Milangaz', location: 'Izmir' },
        { name: 'Güzel Enerji', location: 'Izmir' },
        { name: 'Ege Çelik', location: 'Izmir' },
        { name: 'Batıçim', location: 'Izmir' },
        { name: 'Egegaz', location: 'Izmir' },
        { name: 'Alpet', location: 'Izmir' },
        { name: 'Dikili Limanı', location: 'Izmir' },
        { name: 'TCDD Alsancak', location: 'Izmir' },

        // Kocaeli (İzmit)
        { name: 'Nuh Çimento', location: 'Kocaeli' },
        { name: 'Çolakoğlu', location: 'Kocaeli' },
        { name: 'Ceyport', location: 'Kocaeli' },
        { name: 'Martaş', location: 'Kocaeli' },
        { name: 'Evyap', location: 'Kocaeli' },
        { name: 'Limas', location: 'Kocaeli' },
        { name: 'Solventas', location: 'Kocaeli' },
        { name: 'Altınel', location: 'Kocaeli' },
        { name: 'Poliport', location: 'Kocaeli' },
        { name: 'Gebze Güzel Enerji', location: 'Kocaeli' },
        { name: 'Tüpraş İzmit', location: 'Kocaeli' },
        { name: 'Autoport', location: 'Kocaeli' },
        { name: 'Aslan Çimento', location: 'Kocaeli' },
        { name: 'Diler', location: 'Kocaeli' },
        { name: 'Ford Otosan', location: 'Kocaeli' },
        { name: 'Port Yarımca', location: 'Kocaeli' },
        { name: 'Rota – Altınbaş', location: 'Kocaeli' },

        // Tekirdağ
        { name: 'Asyaport', location: 'Tekirdağ' },
        { name: 'DP World', location: 'Tekirdağ' },
        { name: 'Yılport', location: 'Tekirdağ' },
        { name: 'Beldeport', location: 'Tekirdağ' },

        // Hatay (İskenderun)
        { name: 'Atakaş', location: 'Hatay' },
        { name: 'Limak', location: 'Hatay' },
        { name: 'Sönmez Çimento', location: 'Hatay' },
        { name: 'Yazıcılar', location: 'Hatay' },

        // Adana
        { name: 'Taros Tarım', location: 'Adana' },

        // Balıkesir (Bandırma)
        { name: 'Çelebi Bandırma', location: 'Balıkesir' },
        { name: 'Borusan Bandırma', location: 'Balıkesir' },
        { name: 'Rodaport', location: 'Balıkesir' },

        // Bursa
        { name: 'İÇDAŞ', location: 'Bursa' },

        // Çanakkale
        { name: 'Port of Çanakkale', location: 'Canakkale' },
        { name: 'Akçansa', location: 'Canakkale' },
        { name: 'Kepez', location: 'Canakkale' },

        // Zonguldak
        { name: 'Erdemir', location: 'Zonguldak' },
        { name: 'Filyos', location: 'Zonguldak' },
        { name: 'TTK Zonguldak', location: 'Zonguldak' },

        // Antalya
        { name: 'OPET', location: 'Antalya' },

        // Mersin
        { name: 'MİP', location: 'Mersin' },
        { name: 'ATAŞ', location: 'Mersin' },
        { name: 'AVES', location: 'Mersin' },

        // İstanbul (Ambarlı)
        { name: 'KUMPORT', location: 'Istanbul' },
        { name: 'Haydarpaşa', location: 'Istanbul' },

        // Yalova
        { name: 'AKÇANSA Yalova', location: 'Yalova' },
        { name: 'MARDAŞ', location: 'Yalova' },

        // Bursa (Gemlik)
        { name: 'Zeytport', location: 'Bursa' },
        { name: 'Gemport', location: 'Bursa' },
        { name: 'Roda Port', location: 'Bursa' },
        { name: 'Borusan Gemlik', location: 'Bursa' },

        // Samsun
        { name: 'Esun', location: 'Samsun' },
        { name: 'Ceynak', location: 'Samsun' },
        { name: 'Yeşilyurt', location: 'Samsun' },
        { name: 'Samsunport', location: 'Samsun' },

        // Trabzon
        { name: 'Toros', location: 'Trabzon' },
        { name: 'Sadas', location: 'Trabzon' },

        // Artvin (Hopa)
        { name: 'Altınbaş', location: 'Artvin' },

        // Giresun
        { name: 'ETİ BAKIR', location: 'Giresun' }
      ];

      let filteredPorts = ports;
      
      if (query && typeof query === 'string') {
        const searchTerm = query.toLowerCase();
        filteredPorts = ports.filter(port => 
          port.name.toLowerCase().includes(searchTerm) ||
          port.location.toLowerCase().includes(searchTerm)
        );
      }

      res.status(200).json({
        success: true,
        data: filteredPorts
      });

    } catch (error: any) {
      console.error('Get port suggestions error:', error);
      res.status(500).json({ 
        error: 'Liman önerileri getirilirken hata oluştu',
        details: error.message 
      });
    }
  };

  // Get exchange rates from TCMB
  public getExchangeRates = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const { date } = req.query;

      let rates;
      
      if (date && typeof date === 'string') {
        // Belirli bir tarih için kur çek
        rates = await ExchangeRateService.getRatesForDate(date);
        
        if (!rates) {
          res.status(404).json({
            success: false,
            error: 'Belirtilen tarih için kur bulunamadı'
          });
          return;
        }
        
        // Source bilgisi ekle
        rates = { ...rates, source: 'TCMB' };
      } else {
        // Güncel kurları çek
        rates = await ExchangeRateService.getCurrentRates();
      }

      res.status(200).json({
        success: true,
        data: {
          usdTry: rates.usdTry,
          eurUsd: rates.eurUsd,
          date: rates.date,
          source: rates.source,
          lastUpdated: new Date().toISOString()
        }
      });

    } catch (error: any) {
      console.error('Get exchange rates error:', error);
      
      // Hata durumunda fallback kurlar
      const fallbackRates = {
        usdTry: 30.0,
        eurUsd: 1.1,
        date: new Date().toISOString().split('T')[0],
        source: 'FALLBACK',
        lastUpdated: new Date().toISOString(),
        warning: 'Bu kurlar gerçek veriler değildir. TCMB servisi kullanılamadığı için varsayılan değerler gösterilmektedir.'
      };

      res.status(200).json({
        success: true,
        data: fallbackRates,
        warning: 'Exchange rate service temporarily unavailable, using fallback rates'
      });
    }
  };

  // Get vessel types
  public getVesselTypes = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const vesselTypes = [
        { value: 'passenger', label: 'Passenger Ship' },
        { value: 'ro-ro-ro-pax', label: 'RO-RO / RO-PAX' },
        { value: 'car-carrier', label: 'Car Carrier' },
        { value: 'container', label: 'Container Ship' },
        { value: 'lng-tanker', label: 'LNG Tanker' },
        { value: 'bunker-tanker', label: 'Bunker Tanker' },
        { value: 'other', label: 'Other' }
      ];

      res.status(200).json({
        success: true,
        data: vesselTypes
      });
    } catch (error: any) {
      console.error('Get vessel types error:', error);
      res.status(500).json({
        error: 'Gemi türleri getirilirken hata oluştu',
        details: error.message
      });
    }
  };

  // Get cargo types
  public getCargoTypes = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const cargoTypes = [
        { value: 'general', label: 'General Cargo' },
        { value: 'dangerous', label: 'Dangerous Cargo' }
      ];

      res.status(200).json({
        success: true,
        data: cargoTypes
      });
    } catch (error: any) {
      console.error('Get cargo types error:', error);
      res.status(500).json({
        error: 'Kargo türleri getirilirken hata oluştu',
        details: error.message
      });
    }
  };

  // Get port call types
  public getPortCallTypes = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const portCallTypes = [
        { value: 'simple-port-call', label: 'Simple Port Call' },
        { value: 'bosphorus-transit', label: 'Bosphorus Transit' },
        { value: 'canakkale-transit', label: 'Çanakkale Transit' },
        { value: 'transit-with-port-call', label: 'Transit with Port Call' }
      ];

      res.status(200).json({
        success: true,
        data: portCallTypes
      });
    } catch (error: any) {
      console.error('Get port call types error:', error);
      res.status(500).json({
        error: 'Liman çağrısı türleri getirilirken hata oluştu',
        details: error.message
      });
    }
  };

  // Get flag categories
  public getFlagCategories = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const flagCategories = [
        { value: 'cabotage', label: 'Cabotage (Turkish Coastal)' },
        { value: 'turkish', label: 'Turkish Flag' },
        { value: 'foreign', label: 'Foreign Flag' }
      ];

      res.status(200).json({
        success: true,
        data: flagCategories
      });
    } catch (error: any) {
      console.error('Get flag categories error:', error);
      res.status(500).json({
        error: 'Bayrak kategorileri getirilirken hata oluştu',
        details: error.message
      });
    }
  };

  // Get cargo categories
  public getCargoCategories = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const cargoCategories = [
        { value: 'general', label: 'General Cargo' },
        { value: 'bulk', label: 'Bulk Cargo' },
        { value: 'liquid', label: 'Liquid Cargo' },
        { value: 'container', label: 'Containerized Cargo' },
        { value: 'vehicle', label: 'Vehicles' },
        { value: 'dangerous', label: 'Dangerous Goods' }
      ];

      res.status(200).json({
        success: true,
        data: cargoCategories
      });
    } catch (error: any) {
      console.error('Get cargo categories error:', error);
      res.status(500).json({
        error: 'Kargo kategorileri getirilirken hata oluştu',
        details: error.message
      });
    }
  };

  // Generate PDF for calculation result
  public generateCalculationPDF = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ error: 'Kullanıcı kimlik doğrulaması gerekli' });
        return;
      }

      const { result, vesselName } = req.body;

      if (!result) {
        res.status(400).json({ error: 'Hesaplama sonucu gerekli' });
        return;
      }

      // Generate PDF
      const pdfBuffer = await PDFService.generateCalculationPDF(result, vesselName);

      // Set response headers for PDF download
      const translatedPortName = this.translatePortNameForFilename(result.input.portName);
      const fileName = `Proforma_${translatedPortName}_${new Date().toISOString().split('T')[0]}.pdf`;

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Length', pdfBuffer.length);

      // Send PDF buffer
      res.send(pdfBuffer);

    } catch (error: any) {
      console.error('Generate PDF error:', error);
      res.status(500).json({
        error: 'PDF oluşturulurken hata oluştu',
        details: error.message
      });
    }
  };

  // Helper function to translate port names for filename generation
  private translatePortNameForFilename(portName: string): string {
    // Simple character replacement for filename compatibility
    return portName
      .replace(/ç/g, 'c')
      .replace(/Ç/g, 'C')
      .replace(/ğ/g, 'g')
      .replace(/Ğ/g, 'G')
      .replace(/ı/g, 'i')
      .replace(/İ/g, 'I')
      .replace(/ö/g, 'o')
      .replace(/Ö/g, 'O')
      .replace(/ş/g, 's')
      .replace(/Ş/g, 'S')
      .replace(/ü/g, 'u')
      .replace(/Ü/g, 'U')
      .replace(/[^\w\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '_'); // Replace spaces with underscores
  }
}