import Joi from 'joi';
import { IMaritimeCalculationInput } from './model';

const calculationInputSchema = Joi.object({
  maliyetGrubu: Joi.string()
    .required()
    .messages({
      'any.required': 'Maliyet grubu zorunludur'
    }),
  miktar: Joi.number()
    .positive()
    .required()
    .messages({
      'number.positive': 'Miktar pozitif olmalıdır',
      'any.required': 'Miktar zorunludur'
    }),
  birimFiyat: Joi.number()
    .positive()
    .required()
    .messages({
      'number.positive': 'Birim fiyat pozitif olmalıdır',
      'any.required': 'Birim fiyat zorunludur'
    }),
  kdvOrani: Joi.number()
    .min(0)
    .max(100)
    .required()
    .messages({
      'number.min': 'KDV oranı 0\'dan küçük olamaz',
      'number.max': 'KDV oranı 100\'den büyük olamaz',
      'any.required': 'KDV oranı zorunludur'
    }),
  iskontoOrani: Joi.number()
    .min(0)
    .max(100)
    .default(0)
    .messages({
      'number.min': 'İskonto oranı 0\'dan küçük olamaz',
      'number.max': 'İskonto oranı 100\'den büyük olamaz'
    }),
  ozelDurum: Joi.string()
    .allow('')
    .max(200)
    .messages({
      'string.max': 'Özel durum 200 karakterden fazla olamaz'
    }),
  aciklama: Joi.string()
    .allow('')
    .max(500)
    .messages({
      'string.max': 'Açıklama 500 karakterden fazla olamaz'
    })
});

// Maritime calculation input validation schema
export const maritimeCalculationInputSchema = Joi.object({
  portName: Joi.string().required().trim().min(1).max(100)
    .messages({
      'string.empty': 'Liman adı gereklidir',
      'string.min': 'Liman adı en az 1 karakter olmalıdır',
      'string.max': 'Liman adı en fazla 100 karakter olmalıdır'
    }),
    
  portLocation: Joi.string().required().trim().min(1).max(100)
    .messages({
      'string.empty': 'Liman lokasyonu gereklidir',
      'string.min': 'Liman lokasyonu en az 1 karakter olmalıdır',
      'string.max': 'Liman lokasyonu en fazla 100 karakter olmalıdır'
    }),
    
  portCallType: Joi.string().valid('simple-port-call', 'bosphorus-transit', 'canakkale-transit', 'transit-with-port-call').required()
    .messages({
      'any.only': 'Geçersiz liman çağrısı türü',
      'any.required': 'Liman çağrısı türü gereklidir'
    }),
    
  vesselType: Joi.string().valid('passenger', 'ro-ro-ro-pax', 'car-carrier', 'container', 'lng-tanker', 'bunker-tanker', 'other').required()
    .messages({
      'any.only': 'Geçersiz gemi türü',
      'any.required': 'Gemi türü gereklidir'
    }),
    
  flagCategory: Joi.string().valid('cabotage', 'turkish', 'foreign').required()
    .messages({
      'any.only': 'Geçersiz bayrak kategorisi',
      'any.required': 'Bayrak kategorisi gereklidir'
    }),
    
  grossRegisterTonnage: Joi.number().positive().max(500000).required()
    .messages({
      'number.positive': 'GRT pozitif bir sayı olmalıdır',
      'number.max': 'GRT çok büyük (maksimum 500,000)',
      'any.required': 'GRT gereklidir'
    }),
    
  netRegisterTonnage: Joi.number().positive().max(500000).required()
    .messages({
      'number.positive': 'NRT pozitif bir sayı olmalıdır',
      'number.max': 'NRT çok büyük (maksimum 500,000)',
      'any.required': 'NRT gereklidir'
    }),
    
  deadweightTonnage: Joi.number().positive().max(1000000).required()
    .messages({
      'number.positive': 'DWT pozitif bir sayı olmalıdır',
      'number.max': 'DWT çok büyük (maksimum 1,000,000)',
      'any.required': 'DWT gereklidir'
    }),
    
  daysAtQuay: Joi.number().integer().min(0).max(365).required()
    .when('portCallType', {
      is: Joi.string().valid('bosphorus-transit', 'canakkale-transit'),
      then: Joi.number().valid(0).messages({
        'any.only': 'Sadece geçiş için rıhtım günü sayısı 0 olmalıdır'
      }),
      otherwise: Joi.number().positive().messages({
        'number.positive': 'Liman çağrısı için rıhtım günü sayısı pozitif olmalıdır'
      })
    })
    .messages({
      'number.integer': 'Rıhtımda kalış süresi tam sayı olmalıdır',
      'number.min': 'Rıhtımda kalış süresi negatif olamaz',
      'number.max': 'Rıhtımda kalış süresi çok uzun (maksimum 365 gün)',
      'any.required': 'Rıhtımda kalış süresi gereklidir'
    }),
    
  cargoType: Joi.string().valid('general', 'dangerous').required()
    .messages({
      'any.only': 'Geçersiz yük türü',
      'any.required': 'Yük türü gereklidir'
    }),
    
  cargoQuantity: Joi.number().min(0).max(1000000).optional()
    .messages({
      'number.min': 'Yük miktarı negatif olamaz',
      'number.max': 'Yük miktarı çok büyük'
    }),

  cargoUnits: Joi.number().min(0).max(100000).optional()
    .messages({
      'number.min': 'Yük birimi negatif olamaz',
      'number.max': 'Yük birimi çok büyük'
    }),

  cargoNature: Joi.string().trim().max(200).allow('').optional()
    .messages({
      'string.max': 'Yük niteliği çok uzun (maksimum 200 karakter)'
    }),
    
  cargoCategory: Joi.string().valid(
    // A) DÖKME EŞYA (Bulk Cargo)
    'bulk-solid-minerals',
    'bulk-grains',
    'bulk-legumes',
    'bulk-petroleum',
    'bulk-gas',
    'bulk-chemicals',

    // B) DÖKME OLMAYAN EŞYA (Non-Bulk Cargo)
    'non-bulk-grains',
    'fresh-produce',
    'non-bulk-legumes',
    'steel-paper',
    'wood-logs',

    // C) BOŞ KONTEYNER VE BOŞ TREYLER
    'empty-containers',

    // D) CANLI HAYVANLAR
    'livestock-small',
    'livestock-large',

    // E) DİĞER EŞYA
    'other-cargo-carrier-pays',
    'other-cargo-shipper-pays',

    // F) KONTEYNER İÇİNDE TAŞINAN TÜM YÜKLER
    'containers',
    'transit-containers',

    // G) OTOMOBİL, JEEP, PİKAP, etc.
    'vehicles',

    // H) RO-RO GEMİLERLE TAŞINAN
    'ro-ro-vehicles',

    // Legacy/Backward compatibility
    'general',
    'other'
  ).optional()
    .messages({
      'any.only': 'Geçersiz yük kategorisi'
    }),
    
  usdTryRate: Joi.number().positive().min(1).max(1000).required()
    .messages({
      'number.positive': 'USD/TRY kuru pozitif olmalıdır',
      'number.min': 'USD/TRY kuru çok düşük',
      'number.max': 'USD/TRY kuru çok yüksek',
      'any.required': 'USD/TRY kuru gereklidir'
    }),
    
  eurUsdRate: Joi.number().positive().min(0.5).max(3).optional()
    .messages({
      'number.positive': 'EUR/USD kuru pozitif olmalıdır',
      'number.min': 'EUR/USD kuru çok düşük',
      'number.max': 'EUR/USD kuru çok yüksek'
    }),
    
  specialConditions: Joi.array().items(Joi.string().trim().max(100)).max(10).optional()
    .messages({
      'array.max': 'Çok fazla özel koşul (maksimum 10)'
    }),
    
  isIzmitKorfezi: Joi.boolean().optional(),
  
  isDangerous: Joi.boolean().optional()
});

// Validate maritime calculation input
export const validateMaritimeCalculationInput = (data: any) => {
  return maritimeCalculationInputSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
};

// Save calculation validation schema
export const saveCalculationSchema = Joi.object({
  result: Joi.object().required()
    .messages({
      'any.required': 'Hesaplama sonucu gereklidir'
    }),
    
  title: Joi.string().trim().max(200).optional()
    .messages({
      'string.max': 'Başlık çok uzun (maksimum 200 karakter)'
    }),
    
  description: Joi.string().trim().max(1000).optional()
    .messages({
      'string.max': 'Açıklama çok uzun (maksimum 1000 karakter)'
    })
});

// Validate save calculation request
export const validateSaveCalculation = (data: any) => {
  return saveCalculationSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
};

// Legacy validation schemas (kept for backward compatibility)
export const calculationValidationSchemas = {
  createCalculation: Joi.object({
    inputData: Joi.array().items(
      Joi.object({
        maliyetGrubu: Joi.string().required(),
        miktar: Joi.number().positive().required(),
        birimFiyat: Joi.number().positive().required(),
        kdvOrani: Joi.number().min(0).max(100).required(),
        iskontoOrani: Joi.number().min(0).max(100).optional(),
        ozelDurum: Joi.string().optional(),
        aciklama: Joi.string().optional()
      })
    ).min(1).required(),
    
    calculationType: Joi.string().valid('proforma', 'maliyet', 'kar_zarar').optional(),
    title: Joi.string().max(200).optional(),
    description: Joi.string().max(1000).optional(),
    isPublic: Joi.boolean().optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional()
  }),
  
  updateCalculation: Joi.object({
    title: Joi.string().max(200).optional(),
    description: Joi.string().max(1000).optional(),
    isPublic: Joi.boolean().optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional()
  }),
  
  getCalculations: Joi.object({
    page: Joi.number().integer().min(1).optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    search: Joi.string().max(200).optional(),
    calculationType: Joi.string().valid('proforma', 'maliyet', 'kar_zarar').optional(),
    isPublic: Joi.boolean().optional(),
    tags: Joi.array().items(Joi.string().max(50)).optional(),
    sortBy: Joi.string().valid('createdAt', 'updatedAt', 'title').optional(),
    sortOrder: Joi.string().valid('asc', 'desc').optional()
  })
};

// Validation middleware function
export const validateRequest = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });
    
    if (error) {
      return res.status(400).json({
        error: 'Geçersiz veri',
        details: error.details.map(detail => detail.message)
      });
    }
    
    req.body = value;
    next();
  };
}; 