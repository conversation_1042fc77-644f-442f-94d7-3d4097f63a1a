import { Router } from 'express';
import { AuthController } from './controller';
import { UserService } from '../user/service';
import { protect, validateRefreshToken } from '../../middleware/auth';

const router = Router();

// Auth routes
router.post('/register', AuthController.register);
router.post('/login', AuthController.login);
router.post('/refresh-token', validateRefreshToken, AuthController.refreshToken);
router.post('/logout', protect, AuthController.logout);
router.post('/forgot-password', AuthController.forgotPassword);
router.put('/reset-password/:token', AuthController.resetPassword);
router.get('/verify-email/:token', AuthController.verifyEmail);
router.post('/resend-verification', AuthController.resendVerificationEmail);

// Debug routes for development only
if (process.env.NODE_ENV === 'development') {
  router.get('/test-email', async (req, res) => {
    try {
      console.log('Testing email configuration...');
      const isValid = await UserService.testEmailConfiguration();
      res.json({
        success: true,
        message: isValid ? 'Email configuration is valid' : 'Email configuration has issues',
        data: {
          isValid,
          emailService: process.env.EMAIL_SERVICE,
          emailUser: process.env.EMAIL_USER,
          hasEmailPass: !!process.env.EMAIL_PASS,
          emailPassLength: process.env.EMAIL_PASS?.length
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Email test failed',
        details: error
      });
    }
  });

  // Import UserController for debug route
  const { UserController } = require('../user/controller');
  router.get('/debug-verification/:email', UserController.debugVerificationStatus);
}

export default router;