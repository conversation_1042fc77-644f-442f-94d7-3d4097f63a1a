import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { UserController } from '../user/controller';
import { User } from '../user/model';
import { TokenBlacklistService } from './tokenBlacklist';
import { ClientRequest } from '../../middleware/clientDetection';

interface AuthRequest extends ClientRequest {
  user?: any;
}

interface JWTPayload {
  id: string;
  tokenVersion: number;
  type: 'access' | 'refresh';
  jti?: string;
  iat: number;
  exp: number;
}

export class AuthController {
  // Auth işlemleri için UserController'daki metotları kullanıyoruz
  static register = UserController.register;
  static login = UserController.login;
  static forgotPassword = UserController.forgotPassword;
  static resetPassword = UserController.resetPassword;
  static verifyEmail = UserController.verifyEmail;
  static resendVerificationEmail = UserController.resendVerificationEmail;

  static async refreshToken(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user;

      // Yeni access token oluştur
      const newAccessToken = user.getSignedAccessToken();

      // Yeni refresh token oluştur (token rotation için)
      const newRefreshToken = user.getSignedRefreshToken();

      // Eski refresh token'ı blacklist'e ekle
      let oldRefreshToken;
      if (req.clientType === 'web' && req.cookies?.refreshToken) {
        oldRefreshToken = req.cookies.refreshToken;
      } else {
        oldRefreshToken = req.headers.authorization?.split(' ')[1];
      }

      if (oldRefreshToken) {
        try {
          const refreshSecret = process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET + '_refresh';
          const decoded = jwt.verify(oldRefreshToken, refreshSecret) as JWTPayload;
          if (decoded.jti) {
            await TokenBlacklistService.addToBlacklist(
              decoded.jti,
              user._id.toString(),
              new Date(decoded.exp * 1000)
            );
          }
        } catch (error) {
          // Eski token decode edilemezse devam et
        }
      }

      // Yeni refresh token'ı veritabanına kaydet
      await user.save();

      // Client type'a göre farklı response
      if (req.clientType === 'web') {
        // Web client: refresh token'ı httpOnly cookie olarak gönder
        res.cookie('refreshToken', newRefreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60 * 1000 // 7 gün
        });

        res.status(200).json({
          success: true,
          accessToken: newAccessToken,
          data: {
            id: user._id,
            name: user.name,
            email: user.email,
            isEmailVerified: user.isEmailVerified,
            subscriptionEndDate: user.subscriptionEndDate
          }
        });
      } else {
        // Mobile client: her iki token'ı da response body'de gönder
        res.status(200).json({
          success: true,
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          data: {
            id: user._id,
            name: user.name,
            email: user.email,
            isEmailVerified: user.isEmailVerified,
            subscriptionEndDate: user.subscriptionEndDate
          }
        });
      }
    } catch (error) {
      next(error);
    }
  }

  static async logout(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user;

      if (user) {
        // Kullanıcının tüm token'larını geçersiz kıl
        user.invalidateAllTokens();
        await user.save();

        // Mevcut access token'ı blacklist'e ekle
        const accessToken = req.headers.authorization?.split(' ')[1];
        if (accessToken) {
          try {
            const decoded = jwt.verify(accessToken, process.env.JWT_SECRET || 'fallback-secret') as JWTPayload;
            if (decoded.jti) {
              await TokenBlacklistService.addToBlacklist(
                decoded.jti,
                user._id.toString(),
                new Date(decoded.exp * 1000)
              );
            }
          } catch (error) {
            // Token decode edilemezse devam et
          }
        }
      }

      // Web client için cookie'yi temizle
      if (req.clientType === 'web') {
        res.clearCookie('refreshToken', {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict'
        });
      }

      res.status(200).json({
        success: true,
        message: 'Başarıyla çıkış yapıldı'
      });
    } catch (error) {
      next(error);
    }
  }
}