import mongoose, { Schema, Document } from 'mongoose';

export interface ITokenBlacklist extends Document {
  tokenId: string;
  userId: string;
  expiresAt: Date;
  createdAt: Date;
}

const tokenBlacklistSchema: Schema = new Schema({
  tokenId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  userId: {
    type: String,
    required: true,
    index: true
  },
  expiresAt: {
    type: Date,
    required: true,
    index: { expireAfterSeconds: 0 } // MongoDB TTL index - otomatik silme
  }
}, {
  timestamps: true
});

export const TokenBlacklist = mongoose.model<ITokenBlacklist>('TokenBlacklist', tokenBlacklistSchema);

// Token blacklist utility functions
export class TokenBlacklistService {
  // Token'ı blacklist'e ekle
  static async addToBlacklist(tokenId: string, userId: string, expiresAt: Date): Promise<void> {
    try {
      await TokenBlacklist.create({
        tokenId,
        userId,
        expiresAt
      });
    } catch (error) {
      // Duplicate key error'ı ignore et (token zaten blacklist'te)
      if ((error as any).code !== 11000) {
        throw error;
      }
    }
  }

  // Token blacklist'te mi kontrol et
  static async isBlacklisted(tokenId: string): Promise<boolean> {
    const blacklistedToken = await TokenBlacklist.findOne({ tokenId });
    return !!blacklistedToken;
  }

  // Kullanıcının tüm token'larını blacklist'e ekle
  static async blacklistAllUserTokens(userId: string): Promise<void> {
    // Bu fonksiyon kullanıcının tokenVersion'ını artırdığında kullanılabilir
    // Şu an için sadece placeholder
  }

  // Expired token'ları temizle (manuel cleanup için)
  static async cleanupExpiredTokens(): Promise<void> {
    await TokenBlacklist.deleteMany({
      expiresAt: { $lt: new Date() }
    });
  }
}
