import { Request, Response, NextFunction } from 'express';
import { IyzicoService, IyzicoConfig } from './iyzico.service';
import { SubscriptionService } from '../subscription/service';
import { AuthRequest } from '../../middleware/auth';
import { paymentValidationSchemas } from './payment.validation';

export class PaymentController {
  private static getIyzicoService(): IyzicoService {
    const config: IyzicoConfig = {
      apiKey: process.env.IYZICO_API_KEY || '',
      secretKey: process.env.IYZICO_SECRET_KEY || '',
      uri: process.env.IYZICO_URI || 'https://sandbox-api.iyzipay.com'
    };

    if (!config.apiKey || !config.secretKey) {
      throw new Error('iyzico credentials not configured');
    }

    return new IyzicoService(config);
  }

  // Abonelik ödemesi başlatma
  static async initializeSubscriptionPayment(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = paymentValidationSchemas.initializeSubscriptionPayment.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { subscriptionType } = req.body;
      const userId = req.user._id;
      const userInfo = req.user;
      const ip = req.ip || req.connection.remoteAddress || '127.0.0.1';

      const iyzicoService = PaymentController.getIyzicoService();

      // Ödeme verilerini hazırla
      const paymentData = IyzicoService.prepareSubscriptionPaymentData(
        userId,
        subscriptionType,
        userInfo,
        ip
      );

      // iyzico checkout form başlat
      const result = await iyzicoService.initializeCheckoutForm(paymentData);

      if (result.status === 'success') {
        res.status(200).json({
          success: true,
          data: {
            checkoutFormContent: result.checkoutFormContent,
            token: result.token,
            paymentPageUrl: result.paymentPageUrl
          },
          message: 'Ödeme formu başarıyla oluşturuldu'
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.errorMessage || 'Ödeme formu oluşturulamadı'
        });
      }
    } catch (error) {
      next(error);
    }
  }

  // Ödeme callback işlemi
  static async handlePaymentCallback(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { token } = req.body;

      if (!token) {
        res.status(400).json({
          success: false,
          error: 'Token gerekli'
        });
        return;
      }

      const iyzicoService = PaymentController.getIyzicoService();

      // Ödeme sonucunu al
      const result = await iyzicoService.retrieveCheckoutForm(token);

      if (result.status === 'success' && result.paymentStatus === 'SUCCESS') {
        // Ödeme başarılı, abonelik oluştur
        const conversationId = result.conversationId;
        const userId = conversationId.split('_')[1]; // SUB_userId_timestamp formatından userId'yi çıkar
        
        // Subscription type'ı conversation ID'den çıkar veya basket items'dan al
        const subscriptionType = result.basketItems[0].name.includes('Aylık') ? 'monthly' : 'yearly';

        const subscription = await SubscriptionService.createSubscription(userId, {
          subscriptionType: subscriptionType as 'monthly' | 'yearly',
          paymentDetails: {
            amount: parseFloat(result.paidPrice),
            currency: result.currency,
            transactionId: result.paymentId,
            paymentMethod: 'iyzico'
          }
        });

        res.status(200).json({
          success: true,
          data: {
            subscription,
            paymentResult: result
          },
          message: 'Ödeme başarılı, abonelik oluşturuldu'
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.errorMessage || 'Ödeme başarısız',
          data: result
        });
      }
    } catch (error) {
      next(error);
    }
  }

  // Ödeme durumu sorgulama
  static async getPaymentStatus(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { paymentId, conversationId } = req.params;

      const iyzicoService = PaymentController.getIyzicoService();
      const result = await iyzicoService.retrievePayment(paymentId, conversationId);

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // Ödeme iptali
  static async cancelPayment(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = paymentValidationSchemas.cancelPayment.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { paymentId } = req.body;
      const ip = req.ip || req.connection.remoteAddress || '127.0.0.1';

      const iyzicoService = PaymentController.getIyzicoService();
      const result = await iyzicoService.cancelPayment(paymentId, ip);

      if (result.status === 'success') {
        res.status(200).json({
          success: true,
          data: result,
          message: 'Ödeme başarıyla iptal edildi'
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.errorMessage || 'Ödeme iptal edilemedi'
        });
      }
    } catch (error) {
      next(error);
    }
  }

  // Ödeme iadesi
  static async refundPayment(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = paymentValidationSchemas.refundPayment.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { paymentTransactionId, price } = req.body;
      const ip = req.ip || req.connection.remoteAddress || '127.0.0.1';

      const iyzicoService = PaymentController.getIyzicoService();
      const result = await iyzicoService.refundPayment(paymentTransactionId, price, ip);

      if (result.status === 'success') {
        res.status(200).json({
          success: true,
          data: result,
          message: 'İade işlemi başarıyla tamamlandı'
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.errorMessage || 'İade işlemi başarısız'
        });
      }
    } catch (error) {
      next(error);
    }
  }

  // Taksit bilgilerini alma
  static async getInstallmentInfo(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { binNumber, price } = req.query;

      if (!binNumber || !price) {
        res.status(400).json({
          success: false,
          error: 'BIN numarası ve fiyat gerekli'
        });
        return;
      }

      const iyzicoService = PaymentController.getIyzicoService();
      const result = await iyzicoService.retrieveInstallmentInfo(
        binNumber as string,
        price as string
      );

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // Test endpoint - sadece development için
  static async testIyzicoConnection(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const iyzicoService = PaymentController.getIyzicoService();
      
      // Basit bir test isteği
      const testResult = await iyzicoService.retrieveInstallmentInfo('554960', '100');
      
      res.status(200).json({
        success: true,
        message: 'iyzico bağlantısı başarılı',
        data: testResult
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'iyzico bağlantısı başarısız',
        details: error instanceof Error ? error.message : 'Bilinmeyen hata'
      });
    }
  }
}
