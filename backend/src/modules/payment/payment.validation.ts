import Joi from 'joi';

export const paymentValidationSchemas = {
  initializeSubscriptionPayment: Joi.object({
    subscriptionType: Joi.string()
      .valid('monthly', 'yearly')
      .required()
      .messages({
        'any.only': 'Abonelik türü monthly veya yearly olmalıdır',
        'any.required': 'Abonelik türü zorunludur'
      })
  }),

  cancelPayment: Joi.object({
    paymentId: Joi.string()
      .required()
      .messages({
        'any.required': 'Ödeme ID zorunludur',
        'string.base': 'Ödeme ID string olmalıdır'
      })
  }),

  refundPayment: Joi.object({
    paymentTransactionId: Joi.string()
      .required()
      .messages({
        'any.required': 'Ödeme işlem ID zorunludur',
        'string.base': 'Ödeme işlem ID string olmalıdır'
      }),
    price: Joi.string()
      .required()
      .pattern(/^\d+(\.\d{1,2})?$/)
      .messages({
        'any.required': 'İade tutarı zorunludur',
        'string.pattern.base': 'İade tutarı geçerli bir fiyat formatında olmalıdır (örn: 99.99)'
      })
  }),

  paymentCallback: Joi.object({
    token: Joi.string()
      .required()
      .messages({
        'any.required': 'Token zorunludur',
        'string.base': 'Token string olmalıdır'
      })
  }),

  directPayment: Joi.object({
    subscriptionType: Joi.string()
      .valid('monthly', 'yearly')
      .required()
      .messages({
        'any.only': 'Abonelik türü monthly veya yearly olmalıdır',
        'any.required': 'Abonelik türü zorunludur'
      }),
    paymentCard: Joi.object({
      cardHolderName: Joi.string()
        .min(2)
        .max(50)
        .required()
        .messages({
          'string.min': 'Kart sahibi adı en az 2 karakter olmalıdır',
          'string.max': 'Kart sahibi adı en fazla 50 karakter olmalıdır',
          'any.required': 'Kart sahibi adı zorunludur'
        }),
      cardNumber: Joi.string()
        .pattern(/^\d{16}$/)
        .required()
        .messages({
          'string.pattern.base': 'Kart numarası 16 haneli olmalıdır',
          'any.required': 'Kart numarası zorunludur'
        }),
      expireMonth: Joi.string()
        .pattern(/^(0[1-9]|1[0-2])$/)
        .required()
        .messages({
          'string.pattern.base': 'Son kullanma ayı 01-12 arasında olmalıdır',
          'any.required': 'Son kullanma ayı zorunludur'
        }),
      expireYear: Joi.string()
        .pattern(/^\d{4}$/)
        .required()
        .messages({
          'string.pattern.base': 'Son kullanma yılı 4 haneli olmalıdır',
          'any.required': 'Son kullanma yılı zorunludur'
        }),
      cvc: Joi.string()
        .pattern(/^\d{3,4}$/)
        .required()
        .messages({
          'string.pattern.base': 'CVC 3 veya 4 haneli olmalıdır',
          'any.required': 'CVC zorunludur'
        }),
      registerCard: Joi.string()
        .valid('0', '1')
        .default('0')
        .messages({
          'any.only': 'Kart kaydetme seçeneği 0 veya 1 olmalıdır'
        })
    }).required().messages({
      'any.required': 'Kart bilgileri zorunludur'
    }),
    installment: Joi.string()
      .valid('1', '2', '3', '6', '9', '12')
      .default('1')
      .messages({
        'any.only': 'Taksit sayısı 1, 2, 3, 6, 9 veya 12 olmalıdır'
      })
  }),

  getInstallmentInfo: Joi.object({
    binNumber: Joi.string()
      .pattern(/^\d{6}$/)
      .required()
      .messages({
        'string.pattern.base': 'BIN numarası 6 haneli olmalıdır',
        'any.required': 'BIN numarası zorunludur'
      }),
    price: Joi.string()
      .pattern(/^\d+(\.\d{1,2})?$/)
      .required()
      .messages({
        'string.pattern.base': 'Fiyat geçerli bir format olmalıdır (örn: 99.99)',
        'any.required': 'Fiyat zorunludur'
      })
  })
};
