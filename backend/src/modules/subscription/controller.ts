import { Request, Response, NextFunction } from 'express';
import { SubscriptionService } from './service';
import { subscriptionValidationSchemas } from './validation';

interface AuthRequest extends Request {
  user?: any;
}

export class SubscriptionController {
  static async createSubscription(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = subscriptionValidationSchemas.createSubscription.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { subscriptionType, paymentDetails } = req.body;
      const userId = req.user._id;

      const subscription = await SubscriptionService.createSubscription(userId, {
        subscriptionType,
        paymentDetails
      });

      res.status(201).json({
        success: true,
        data: subscription,
        message: 'Abonelik başarıyla oluşturuldu'
      });
    } catch (error) {
      next(error);
    }
  }

  static async extendSubscription(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { error } = subscriptionValidationSchemas.extendSubscription.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message
        });
        return;
      }

      const { days, paymentDetails } = req.body;
      const userId = req.user._id;

      const subscription = await SubscriptionService.extendSubscription(userId, days, paymentDetails);

      res.status(200).json({
        success: true,
        data: subscription,
        message: `Abonelik ${days} gün uzatıldı`
      });
    } catch (error) {
      next(error);
    }
  }

  static async getActiveSubscription(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user._id;
      const subscription = await SubscriptionService.getActiveSubscription(userId);

      res.status(200).json({
        success: true,
        data: subscription
      });
    } catch (error) {
      next(error);
    }
  }

  static async getSubscriptionHistory(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user._id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const historyData = await SubscriptionService.getSubscriptionHistory(userId, page, limit);

      res.status(200).json({
        success: true,
        data: historyData
      });
    } catch (error) {
      next(error);
    }
  }

  static async getSubscriptionStats(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user._id;
      const stats = await SubscriptionService.getSubscriptionStats(userId);

      res.status(200).json({
        success: true,
        data: stats
      });
    } catch (error) {
      next(error);
    }
  }

  static async cancelSubscription(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user._id;
      const success = await SubscriptionService.cancelSubscription(userId);

      if (!success) {
        res.status(404).json({
          success: false,
          error: 'İptal edilecek aktif abonelik bulunamadı'
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Abonelik başarıyla iptal edildi'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getSubscriptionPricing(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const pricing = SubscriptionService.getSubscriptionPricing();

      res.status(200).json({
        success: true,
        data: pricing
      });
    } catch (error) {
      next(error);
    }
  }

  // Admin için - süresi dolan abonelikleri deaktif et
  static async deactivateExpiredSubscriptions(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const deactivatedCount = await SubscriptionService.deactivateExpiredSubscriptions();

      res.status(200).json({
        success: true,
        data: {
          deactivatedCount
        },
        message: `${deactivatedCount} abonelik deaktif edildi`
      });
    } catch (error) {
      next(error);
    }
  }
} 