import { Subscription, ISubscription } from './model';
import { User } from '../user/model';
import mongoose from 'mongoose';

export class SubscriptionService {
  static async createSubscription(userId: string, subscriptionData: {
    subscriptionType: 'monthly' | 'yearly';
    paymentDetails: {
      amount: number;
      currency: string;
      transactionId: string;
      paymentMethod: string;
    };
  }): Promise<ISubscription> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Mevcut aktif abonelikleri deaktif et
      await Subscription.updateMany(
        { userId, isActive: true },
        { isActive: false },
        { session }
      );

      // Abonelik süresi hesapla
      const startDate = new Date();
      const endDate = new Date();
      
      if (subscriptionData.subscriptionType === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else if (subscriptionData.subscriptionType === 'yearly') {
        endDate.setFullYear(endDate.getFullYear() + 1);
      }

      // <PERSON><PERSON> abonelik oluştur
      const newSubscription = await Subscription.create([{
        userId,
        subscriptionType: subscriptionData.subscriptionType,
        startDate,
        endDate,
        isActive: true,
        paymentDetails: {
          ...subscriptionData.paymentDetails,
          paymentDate: new Date()
        }
      }], { session });

      // Kullanıcının abonelik bitiş tarihini güncelle
      await User.findByIdAndUpdate(
        userId,
        { subscriptionEndDate: endDate },
        { session }
      );

      await session.commitTransaction();
      return newSubscription[0];
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async getUserSubscriptions(userId: string): Promise<ISubscription[]> {
    return await Subscription.find({ userId })
      .sort({ createdAt: -1 })
      .populate('userId', 'name email');
  }

  static async getActiveSubscription(userId: string): Promise<ISubscription | null> {
    const subscription = await Subscription.findOne({
      userId,
      isActive: true,
      endDate: { $gt: new Date() }
    });

    if (subscription) {
      // Durumu kontrol et ve güncelle
      const isStillActive = subscription.endDate > new Date();
      if (!isStillActive) {
        subscription.isActive = false;
        await subscription.save();
        return null;
      }
    }

    return subscription;
  }

  static async extendSubscription(userId: string, days: number, paymentDetails: {
    amount: number;
    currency: string;
    transactionId: string;
    paymentMethod: string;
  }): Promise<ISubscription> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const user = await User.findById(userId).session(session);
      if (!user) {
        throw new Error('Kullanıcı bulunamadı');
      }

      // Mevcut abonelik bitiş tarihini al, yoksa şu anki tarihi kullan
      const currentEndDate = user.subscriptionEndDate || new Date();
      const newEndDate = new Date(currentEndDate.getTime() + days * 24 * 60 * 60 * 1000);

      // Yeni abonelik kaydı oluştur
      const newSubscription = await Subscription.create([{
        userId,
        subscriptionType: days <= 31 ? 'monthly' : 'yearly',
        startDate: new Date(),
        endDate: newEndDate,
        isActive: true,
        paymentDetails: {
          ...paymentDetails,
          paymentDate: new Date()
        }
      }], { session });

      // Kullanıcının abonelik bitiş tarihini güncelle
      await User.findByIdAndUpdate(
        userId,
        { subscriptionEndDate: newEndDate },
        { session }
      );

      await session.commitTransaction();
      return newSubscription[0];
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async cancelSubscription(userId: string): Promise<boolean> {
    const result = await Subscription.updateMany(
      { userId, isActive: true },
      { isActive: false }
    );

    return result.modifiedCount > 0;
  }

  static async getSubscriptionHistory(userId: string, page: number = 1, limit: number = 10): Promise<{
    subscriptions: ISubscription[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
  }> {
    const skip = (page - 1) * limit;

    const [subscriptions, totalCount] = await Promise.all([
      Subscription.find({ userId })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Subscription.countDocuments({ userId })
    ]);

    return {
      subscriptions,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page
    };
  }

  static async getSubscriptionStats(userId: string): Promise<{
    totalSubscriptions: number;
    totalSpent: number;
    currentSubscription: ISubscription | null;
    subscriptionHistory: ISubscription[];
  }> {
    const [totalSubscriptions, currentSubscription, subscriptionHistory] = await Promise.all([
      Subscription.countDocuments({ userId }),
      this.getActiveSubscription(userId),
      Subscription.find({ userId }).sort({ createdAt: -1 }).limit(5)
    ]);

    // Toplam harcama hesapla
    const totalSpent = await Subscription.aggregate([
      { $match: { userId: new mongoose.Types.ObjectId(userId) } },
      { $group: { _id: null, total: { $sum: '$paymentDetails.amount' } } }
    ]);

    return {
      totalSubscriptions,
      totalSpent: totalSpent[0]?.total || 0,
      currentSubscription,
      subscriptionHistory
    };
  }

  // Süresi dolan abonelikleri otomatik olarak deaktif et
  static async deactivateExpiredSubscriptions(): Promise<number> {
    const result = await Subscription.updateMany(
      {
        isActive: true,
        endDate: { $lt: new Date() }
      },
      { isActive: false }
    );

    return result.modifiedCount;
  }

  // Abonelik fiyatlandırması
  static getSubscriptionPricing(): {
    monthly: { price: number; currency: string };
    yearly: { price: number; currency: string; discount: number };
  } {
    return {
      monthly: {
        price: 99,
        currency: 'TRY'
      },
      yearly: {
        price: 999,
        currency: 'TRY',
        discount: 20 // %20 indirim
      }
    };
  }
} 