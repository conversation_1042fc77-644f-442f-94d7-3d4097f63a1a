import mongoose, { Document, Schema } from 'mongoose';

export interface ISubscription extends Document {
  userId: mongoose.Types.ObjectId;
  subscriptionType: 'trial' | 'monthly' | 'yearly';
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  paymentDetails?: {
    amount: number;
    currency: string;
    transactionId: string;
    paymentMethod: string;
    paymentDate: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

const subscriptionSchema: Schema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  subscriptionType: {
    type: String,
    enum: ['trial', 'monthly', 'yearly'],
    required: true,
    default: 'trial'
  },
  startDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  endDate: {
    type: Date,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  paymentDetails: {
    amount: {
      type: Number,
      required: function(this: ISubscription) {
        return this.subscriptionType !== 'trial';
      }
    },
    currency: {
      type: String,
      default: 'TRY'
    },
    transactionId: {
      type: String,
      required: function(this: ISubscription) {
        return this.subscriptionType !== 'trial';
      }
    },
    paymentMethod: {
      type: String,
      enum: ['credit_card', 'bank_transfer', 'paypal', 'iyzico'],
      required: function(this: ISubscription) {
        return this.subscriptionType !== 'trial';
      }
    },
    paymentDate: {
      type: Date,
      required: function(this: ISubscription) {
        return this.subscriptionType !== 'trial';
      }
    }
  }
}, {
  timestamps: true
});

// Index'ler
subscriptionSchema.index({ userId: 1 });
subscriptionSchema.index({ endDate: 1 });
subscriptionSchema.index({ isActive: 1 });

// Virtual field - kalan gün sayısı
subscriptionSchema.virtual('daysLeft').get(function(this: ISubscription) {
  const now = new Date();
  const timeDiff = this.endDate.getTime() - now.getTime();
  return Math.max(0, Math.ceil(timeDiff / (1000 * 3600 * 24)));
});

// Method - abonelik durumunu kontrol et
subscriptionSchema.methods.checkStatus = function(this: ISubscription) {
  const now = new Date();
  this.isActive = now < this.endDate;
  return this.isActive;
};

export const Subscription = mongoose.model<ISubscription>('Subscription', subscriptionSchema); 