import { Router } from 'express';
import { SubscriptionController } from './controller';
import { protect } from '../../middleware/auth';

const router = Router();

// Public routes
router.get('/pricing', SubscriptionController.getSubscriptionPricing);

// Protected routes
router.use(protect); // Tüm aşağıdaki routes için auth gerekli

router.post('/', SubscriptionController.createSubscription);
router.post('/extend', SubscriptionController.extendSubscription);
router.get('/active', SubscriptionController.getActiveSubscription);
router.get('/history', SubscriptionController.getSubscriptionHistory);
router.get('/stats', SubscriptionController.getSubscriptionStats);
router.delete('/cancel', SubscriptionController.cancelSubscription);

// Admin routes (şimdilik sadece protect, ileride admin middleware eklenebilir)
router.post('/admin/deactivate-expired', SubscriptionController.deactivateExpiredSubscriptions);

export default router; 