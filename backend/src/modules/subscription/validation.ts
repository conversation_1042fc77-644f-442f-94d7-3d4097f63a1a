import Joi from 'joi';

export const subscriptionValidationSchemas = {
  createSubscription: Joi.object({
    subscriptionType: Joi.string()
      .valid('monthly', 'yearly')
      .required()
      .messages({
        'any.only': 'Abonelik türü monthly veya yearly olmalıdır',
        'any.required': 'Abonelik türü zorunludur'
      }),
    paymentDetails: Joi.object({
      amount: Joi.number()
        .positive()
        .required()
        .messages({
          'number.positive': 'Ödeme tutarı pozitif olmalıdır',
          'any.required': 'Ödeme tutarı zorunludur'
        }),
      currency: Joi.string()
        .default('TRY')
        .messages({
          'string.base': 'Para birimi string olmalıdır'
        }),
      transactionId: Joi.string()
        .required()
        .messages({
          'any.required': 'İşlem ID zorunludur'
        }),
      paymentMethod: Joi.string()
        .valid('credit_card', 'bank_transfer', 'paypal', 'iyzico')
        .required()
        .messages({
          'any.only': 'Ödeme yöntemi credit_card, bank_transfer, paypal veya iyzico olmalıdır',
          'any.required': 'Ödeme yöntemi zorunludur'
        })
    }).required().messages({
      'any.required': 'Ödeme detayları zorunludur'
    })
  }),

  extendSubscription: Joi.object({
    days: Joi.number()
      .integer()
      .min(1)
      .max(365)
      .required()
      .messages({
        'number.integer': 'Gün sayısı tam sayı olmalıdır',
        'number.min': 'Gün sayısı en az 1 olmalıdır',
        'number.max': 'Gün sayısı en fazla 365 olmalıdır',
        'any.required': 'Gün sayısı zorunludur'
      }),
    paymentDetails: Joi.object({
      amount: Joi.number()
        .positive()
        .required()
        .messages({
          'number.positive': 'Ödeme tutarı pozitif olmalıdır',
          'any.required': 'Ödeme tutarı zorunludur'
        }),
      currency: Joi.string()
        .default('TRY'),
      transactionId: Joi.string()
        .required()
        .messages({
          'any.required': 'İşlem ID zorunludur'
        }),
      paymentMethod: Joi.string()
        .valid('credit_card', 'bank_transfer', 'paypal', 'iyzico')
        .required()
        .messages({
          'any.only': 'Ödeme yöntemi credit_card, bank_transfer, paypal veya iyzico olmalıdır',
          'any.required': 'Ödeme yöntemi zorunludur'
        })
    }).required().messages({
      'any.required': 'Ödeme detayları zorunludur'
    })
  })
}; 