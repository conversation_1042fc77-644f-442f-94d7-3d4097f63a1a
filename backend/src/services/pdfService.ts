import PDFDocument from 'pdfkit';
import { IMaritimeCalculationResult } from '../modules/calculation/model';

export class PDFService {
  static generateCalculationPDF(result: IMaritimeCalculationResult, vesselName?: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ margin: 50 });
        const buffers: Buffer[] = [];

        doc.on('data', buffers.push.bind(buffers));
        doc.on('end', () => {
          const pdfBuffer = Buffer.concat(buffers);
          resolve(pdfBuffer);
        });

        // Header
        doc.fontSize(20)
           .font('Helvetica-Bold')
           .text('TURKISH PORTS PROFORMA CALCULATION', { align: 'center' });
        
        doc.fontSize(14)
           .font('Helvetica')
           .text('Maritime Port Fee Calculation Report', { align: 'center' });
        
        doc.moveDown(2);

        // Vessel and Port Information
        doc.fontSize(14)
           .font('Helvetica-Bold')
           .text('VESSEL AND PORT INFORMATION');
        
        doc.moveDown(0.5);
        doc.fontSize(10)
           .font('Helvetica');

        const vesselInfo = [
          `Vessel Name: ${vesselName || 'Container Ship'}`,
          `Port: ${this.translatePortNameToEnglish(result.input.portName)}`,
          `Port Location: ${this.translatePortLocationToEnglish(result.input.portLocation)}`,
          `Vessel Type: ${this.getVesselTypeEnglishLabel(result.input.vesselType)}`,
          `Flag Category: ${this.getFlagCategoryEnglishLabel(result.input.flagCategory)}`,
          `Gross Register Tonnage (GRT): ${result.input.grossRegisterTonnage.toLocaleString()}`,
          `Net Register Tonnage (NRT): ${result.input.netRegisterTonnage.toLocaleString()}`,
          `Deadweight Tonnage (DWT): ${result.input.deadweightTonnage?.toLocaleString() || 'Not specified'}`,
          `Port Stay Duration: ${result.input.daysAtQuay} days`,
          `Calculation Date: ${new Date().toLocaleDateString('en-US')}`
        ];

        vesselInfo.forEach(info => {
          doc.text(info);
          doc.moveDown(0.3);
        });

        doc.moveDown(1);

        // Exchange Rates
        doc.fontSize(12)
           .font('Helvetica-Bold')
           .text('EXCHANGE RATES');
        
        doc.moveDown(0.5);
        doc.fontSize(10)
           .font('Helvetica')
           .text(`USD/TRY: ${result.input.usdTryRate.toFixed(4)}`)
           .text(`EUR/USD: ${result.input.eurUsdRate?.toFixed(4) || 'N/A'}`);

        doc.moveDown(1);

        // Fee Breakdown
        doc.fontSize(12)
           .font('Helvetica-Bold')
           .text('FEE DETAILS');
        
        doc.moveDown(0.5);

        const fees = [
          { title: '1. PILOTAGE', amount: result.fees.pilotage.total, calculation: this.cleanCalculationString(result.fees.pilotage.calculation) },
          { title: '2. TUGBOAT', amount: result.fees.tugboat.total, calculation: this.cleanCalculationString(result.fees.tugboat.calculation) },
          { title: '3. MOORING', amount: result.fees.mooring.total, calculation: this.cleanCalculationString(result.fees.mooring.calculation) },
          { title: '4. QUAY DUE', amount: result.fees.quayDue.total, calculation: this.cleanCalculationString(result.fees.quayDue.calculation) },
          { title: '5. GARBAGE COLLECTION', amount: result.fees.garbageCollection.usdAmount, calculation: this.cleanCalculationString(result.fees.garbageCollection.calculation) },
          { title: '6. SANITARY FEE', amount: result.fees.sanitaryFee.usdAmount, calculation: this.cleanCalculationString(result.fees.sanitaryFee.calculation) },
          { title: '7. LIGHT DUE', amount: result.fees.lightDue.total, calculation: this.cleanCalculationString(result.fees.lightDue.calculation) },
          { title: '9. HARBOUR MASTER FEE', amount: result.fees.harbourMasterFee.usdAmount, calculation: this.cleanCalculationString(result.fees.harbourMasterFee.calculation) },
          { title: '10. CHAMBER OF SHIPPING', amount: result.fees.chamberOfShipping.usdAmount, calculation: this.cleanCalculationString(result.fees.chamberOfShipping.calculation) },
          { title: '11. AGENCY FEE', amount: result.fees.agencyFee.usdAmount, calculation: this.cleanCalculationString(result.fees.agencyFee.calculation) },
          { title: '12. ATTENDANCE FEE', amount: result.fees.attendanceSupervisionFee.usdAmount, calculation: this.cleanCalculationString(result.fees.attendanceSupervisionFee.calculation) }
        ];

        // Add vessel traffic fee if exists
        if (result.fees.vesselTrafficFee) {
          fees.splice(7, 0, {
            title: '8. VESSEL TRAFFIC FEE',
            amount: result.fees.vesselTrafficFee.total,
            calculation: `Entry: ${this.formatCurrency(result.fees.vesselTrafficFee.entry)}, Departure: ${this.formatCurrency(result.fees.vesselTrafficFee.departure)}`
          });
        }

        // Add freight share if exists
        if (result.fees.freightShare && result.fees.freightShare.usdAmount > 0) {
          fees.push({
            title: 'FREIGHT SHARE',
            amount: result.fees.freightShare.usdAmount,
            calculation: this.cleanCalculationString(result.fees.freightShare.calculation)
          });
        }

        // Add transit fees if exists
        if (result.fees.transitFees) {
          fees.push({
            title: 'Bosphorus/Canakkale Transit Fees',
            amount: result.fees.transitFees.total,
            calculation: `Pilotage: ${this.formatCurrency(result.fees.transitFees.transitPilotage)}, Tugboat: ${this.formatCurrency(result.fees.transitFees.transitTugboat || 0)}, Sanitary: ${this.formatCurrency(result.fees.transitFees.transitSanitary)}, Light: ${this.formatCurrency(result.fees.transitFees.transitLight)}, Agency: ${this.formatCurrency(result.fees.transitFees.transitAgency)}`
          });
        }

        fees.forEach(fee => {
          // Check if we need a new page
          if (doc.y > 700) {
            doc.addPage();
          }

          doc.fontSize(10)
             .font('Helvetica-Bold')
             .text(fee.title, { continued: true })
             .text(this.formatCurrency(fee.amount), { align: 'right' });
          
          doc.moveDown(0.3);
          
          // Add calculation details
          doc.fontSize(8)
             .font('Helvetica')
             .fillColor('gray')
             .text(fee.calculation, { indent: 20 });
          
          doc.fillColor('black');
          doc.moveDown(0.5);
        });

        // Add totals
        doc.moveDown(1);
        
        if (doc.y > 650) {
          doc.addPage();
        }

        doc.fontSize(12)
           .font('Helvetica-Bold')
           .text('TOTAL AMOUNTS');
        
        doc.moveDown(0.5);
        doc.fontSize(10);

        if (result.subtotals.transitServices) {
          doc.text('Port Call Services Total:', { continued: true })
             .text(this.formatCurrency(result.subtotals.portCallServices), { align: 'right' });
          
          doc.text('Transit Services Total:', { continued: true })
             .text(this.formatCurrency(result.subtotals.transitServices), { align: 'right' });
        }

        doc.fontSize(14)
           .font('Helvetica-Bold')
           .text('GRAND TOTAL:', { continued: true })
           .text(this.formatCurrency(result.grandTotal), { align: 'right' });

        doc.moveDown(2);

        // Add notes if any
        if (result.calculationNotes && result.calculationNotes.length > 0) {
          if (doc.y > 600) {
            doc.addPage();
          }

          doc.fontSize(10)
             .font('Helvetica-Bold')
             .text('CALCULATION NOTES');
          
          doc.moveDown(0.5);
          doc.fontSize(9)
             .font('Helvetica');

          result.calculationNotes.forEach(note => {
            // Translate notes to English
            const englishNote = this.translateNoteToEnglish(note);
            doc.text(`• ${englishNote}`);
            doc.moveDown(0.3);
          });
        }

        // Dangerous cargo note is already included in calculationNotes, no need to add separately

        // Add footer to current page only (PDFKit doesn't support easy multi-page footer)
        doc.fontSize(8)
           .fillColor('gray')
           .text(`Generated: ${new Date().toLocaleString('en-US')}`, 50, doc.page.height - 35);

        doc.end();

      } catch (error) {
        reject(error);
      }
    });
  }

  private static getVesselTypeEnglishLabel(vesselType: string): string {
    switch (vesselType) {
      case 'passenger':
        return 'Passenger Ship';
      case 'ro-ro-ro-pax':
        return 'RO-RO Passenger Ship';
      case 'car-carrier':
        return 'Car Carrier';
      case 'container':
        return 'Container Ship';
      case 'lng-tanker':
        return 'LNG Tanker';
      case 'bunker-tanker':
        return 'Bunker Tanker';
      case 'other':
        return 'Other Ship';
      default:
        return vesselType;
    }
  }

  private static getFlagCategoryEnglishLabel(flagCategory: string): string {
    switch (flagCategory) {
      case 'cabotage':
        return 'Turkish Flag (Cabotage)';
      case 'turkish':
        return 'Turkish Flag';
      case 'foreign':
        return 'Foreign Flag';
      default:
        return flagCategory;
    }
  }

  private static translateNoteToEnglish(note: string): string {
    // Notes are already in English from backend calculation service
    // No translation needed anymore, just return as is
    return note;
  }

  private static cleanCalculationString(calculation: string): string {
    // Remove any Turkish characters and fix encoding issues
    return calculation
      .replace(/[İıĞğÜüŞşÖöÇç]/g, (match) => {
        const replacements: { [key: string]: string } = {
          'İ': 'I', 'ı': 'i', 'Ğ': 'G', 'ğ': 'g',
          'Ü': 'U', 'ü': 'u', 'Ş': 'S', 'ş': 's',
          'Ö': 'O', 'ö': 'o', 'Ç': 'C', 'ç': 'c'
        };
        return replacements[match] || match;
      })
      .replace(/[^\x00-\x7F]/g, '') // Remove any non-ASCII characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  private static translatePortNameToEnglish(portName: string): string {
    // Map Turkish port names to English equivalents (removing Turkish characters)
    const portNameMap: { [key: string]: string } = {
      // İzmir ports
      'İzmir Demir Çelik (İDC)': 'Izmir Demir Celik (IDC)',
      'Batıliman': 'Batiliman',
      'Tüpraş': 'Tupras',
      'Güzel Enerji': 'Guzel Enerji',
      'Ege Çelik': 'Ege Celik',
      'Batıçim': 'Baticim',
      'Dikili Limanı': 'Dikili Limani',

      // Kocaeli ports
      'Nuh Çimento': 'Nuh Cimento',
      'Çolakoğlu': 'Colakoglu',
      'Martaş': 'Martas',
      'Tüpraş İzmit': 'Tupras Izmit',
      'Aslan Çimento': 'Aslan Cimento',
      'Rota – Altınbaş': 'Rota - Altinbas',
      'Yarımca': 'Yarimca',

      // Tekirdağ ports
      'Yılport': 'Yilport',

      // Hatay ports
      'Atakaş': 'Atakas',
      'Sönmez Çimento': 'Sonmez Cimento',
      'Yazıcılar': 'Yazicilar',

      // Adana ports
      'Taros Tarım': 'Taros Tarim',

      // Balıkesir ports
      'Çelebi Bandırma': 'Celebi Bandirma',
      'Borusan Bandırma': 'Borusan Bandirma',

      // Bursa ports
      'İÇDAŞ': 'ICDAS',

      // Çanakkale ports
      'Port of Çanakkale': 'Port of Canakkale',
      'Akçansa': 'Akcansa',

      // Antalya ports
      'OPET': 'OPET',

      // Mersin ports
      'MİP': 'MIP',
      'ATAŞ': 'ATAS',

      // İstanbul ports
      'Haydarpaşa': 'Haydarpasa',

      // Yalova ports
      'AKÇANSA Yalova': 'AKCANSA Yalova',
      'MARDAŞ': 'MARDAS',

      // Samsun ports
      'Yeşilyurt': 'Yesilyurt',

      // Trabzon ports
      'Şadas': 'Sadas',

      // Artvin ports
      'Altınbaş': 'Altinbas',

      // Giresun ports
      'ETİ BAKIR': 'ETI BAKIR'
    };

    return portNameMap[portName] || portName;
  }

  private static translatePortLocationToEnglish(portLocation: string): string {
    // Map Turkish port location names to English equivalents
    const locationMap: { [key: string]: string } = {
      'İzmir': 'Izmir',
      'İstanbul': 'Istanbul',
      'Kocaeli': 'Kocaeli',
      'Tekirdağ': 'Tekirdag',
      'Hatay': 'Hatay',
      'Adana': 'Adana',
      'Balıkesir': 'Balikesir',
      'Bursa': 'Bursa',
      'Çanakkale': 'Canakkale',
      'Zonguldak': 'Zonguldak',
      'Antalya': 'Antalya',
      'Mersin': 'Mersin',
      'Yalova': 'Yalova',
      'Samsun': 'Samsun',
      'Trabzon': 'Trabzon',
      'Artvin': 'Artvin',
      'Giresun': 'Giresun'
    };

    return locationMap[portLocation] || portLocation;
  }

  // Compatibility methods - keep old names but use new English labels
  private static getVesselTypeLabel(vesselType: string): string {
    return this.getVesselTypeEnglishLabel(vesselType);
  }

  private static getFlagCategoryLabel(flagCategory: string): string {
    return this.getFlagCategoryEnglishLabel(flagCategory);
  }

  private static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }
}
