import { Request, Response, NextFunction } from 'express';

export interface ClientRequest extends Request {
  clientType?: 'web' | 'mobile';
}

export type ClientType = 'web' | 'mobile';

export const detectClientType = (req: ClientRequest, res: Response, next: NextFunction): void => {
  // Check for X-Client-Type header first
  const clientTypeHeader = req.headers['x-client-type'] as string;
  
  if (clientTypeHeader) {
    const normalizedClientType = clientTypeHeader.toLowerCase();
    if (normalizedClientType === 'web' || normalizedClientType === 'mobile') {
      req.clientType = normalizedClientType as ClientType;
      next();
      return;
    }
  }

  // Fallback to User-Agent detection if X-Client-Type is not provided
  const userAgent = req.headers['user-agent'] || '';
  
  // Simple mobile detection based on User-Agent
  const mobileKeywords = [
    'mobile', 'android', 'iphone', 'ipad', 'ipod', 
    'blackberry', 'windows phone', 'opera mini'
  ];
  
  const isMobile = mobileKeywords.some(keyword => 
    userAgent.toLowerCase().includes(keyword)
  );
  
  req.clientType = isMobile ? 'mobile' : 'web';
  next();
};

export default detectClientType;
