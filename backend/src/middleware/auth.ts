import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User } from '../modules/user/model';
import { TokenBlacklistService } from '../modules/auth/tokenBlacklist';
import { ClientRequest } from './clientDetection';

export interface AuthRequest extends ClientRequest {
  user?: any;
}

interface JWTPayload {
  id: string;
  tokenVersion: number;
  type: 'access' | 'refresh';
  jti?: string;
  iat: number;
  exp: number;
}

export const protect = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    let token;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      res.status(401).json({
        success: false,
        error: 'Bu işlem için giriş yapmanız gerekiyor'
      });
      return;
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as JWTPayload;

      // Token tipini kontrol et (sadece access token kabul et)
      if (decoded.type !== 'access') {
        res.status(401).json({
          success: false,
          error: 'Geçersiz token tipi'
        });
        return;
      }

      // Token blacklist'te mi kontrol et
      if (decoded.jti && await TokenBlacklistService.isBlacklisted(decoded.jti)) {
        res.status(401).json({
          success: false,
          error: 'Token geçersiz kılınmış'
        });
        return;
      }

      const user = await User.findById(decoded.id);

      if (!user) {
        res.status(401).json({
          success: false,
          error: 'Kullanıcı bulunamadı'
        });
        return;
      }

      // Token version kontrolü
      if (decoded.tokenVersion !== user.tokenVersion) {
        res.status(401).json({
          success: false,
          error: 'Token geçersiz - yeniden giriş yapın'
        });
        return;
      }

      req.user = user;
      next();
    } catch (error) {
      res.status(401).json({
        success: false,
        error: 'Geçersiz token'
      });
      return;
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Sunucu hatası'
    });
    return;
  }
};

// Alias for protect
export const auth = protect;

// Refresh token doğrulama middleware'i
export const validateRefreshToken = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    let token;

    // For web clients, try to get refresh token from cookie first
    if (req.clientType === 'web' && req.cookies?.refreshToken) {
      token = req.cookies.refreshToken;
    }
    // For mobile clients or fallback, get from Authorization header
    else if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      res.status(401).json({
        success: false,
        error: 'Refresh token gerekli'
      });
      return;
    }

    try {
      const refreshSecret = process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET + '_refresh';
      const decoded = jwt.verify(token, refreshSecret) as JWTPayload;

      // Token tipini kontrol et (sadece refresh token kabul et)
      if (decoded.type !== 'refresh') {
        res.status(401).json({
          success: false,
          error: 'Geçersiz token tipi'
        });
        return;
      }

      // Token blacklist'te mi kontrol et
      if (decoded.jti && await TokenBlacklistService.isBlacklisted(decoded.jti)) {
        res.status(401).json({
          success: false,
          error: 'Refresh token geçersiz kılınmış'
        });
        return;
      }

      const user = await User.findById(decoded.id);

      if (!user) {
        res.status(401).json({
          success: false,
          error: 'Kullanıcı bulunamadı'
        });
        return;
      }

      // Token version kontrolü
      if (decoded.tokenVersion !== user.tokenVersion) {
        res.status(401).json({
          success: false,
          error: 'Refresh token geçersiz - yeniden giriş yapın'
        });
        return;
      }

      // Refresh token veritabanında geçerli mi kontrol et
      if (!user.validateRefreshToken(token)) {
        res.status(401).json({
          success: false,
          error: 'Refresh token geçersiz veya süresi dolmuş'
        });
        return;
      }

      req.user = user;
      next();
    } catch (error) {
      res.status(401).json({
        success: false,
        error: 'Geçersiz refresh token'
      });
      return;
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Sunucu hatası'
    });
    return;
  }
};

export const checkSubscription = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const user = req.user;

    if (!user.subscriptionEndDate) {
      res.status(403).json({
        success: false,
        error: 'Abonelik bulunamadı'
      });
      return;
    }

    if (new Date() > user.subscriptionEndDate) {
      res.status(403).json({
        success: false,
        error: 'Aboneliğinizin süresi dolmuş. Hesaplamalar için yeniden abone olmanız gerekiyor.'
      });
      return;
    }

    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Sunucu hatası'
    });
    return;
  }
}; 