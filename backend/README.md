# PROFORMA Backend

Bu proje, PROFORMA hesaplama uygulamasının backend kısmıdır. TypeScript, Express.js ve MongoDB kullanılarak geliştirilmiştir.

## Özellikler

- ✅ **Kullanıcı Yönetimi**: Kayıt, giriş, çık<PERSON>ş, ş<PERSON>re s<PERSON>ırl<PERSON>
- ✅ **<PERSON>ail <PERSON>**: Kullanıcı email doğrulama sistemi
- ✅ **Abonelik Sistemi**: 3 günlük deneme süresi, aylık/yıllık abonelikler
- ✅ **PROFORMA Hesaplamaları**: KDV, iskonto, maliyet hesaplamaları
- ✅ **Güvenlik**: JWT tokenları, rate limiting, helmet güvenlik
- ✅ **Modüler Yapı**: Controller-Service-Model-Validation yapısı

## Teknolojiler

- **Node.js** - Runtime
- **Express.js** - Web framework
- **TypeScript** - Type safety
- **MongoDB** - Veritabanı
- **Mongoose** - ODM
- **JWT** - Authentication
- **Joi** - Validation
- **Bcrypt** - Şifre hashleme
- **Nodemailer** - Email gönderimi

## Kurulum

1. **Bağımlılıkları yükleyin:**
```bash
npm install
```

2. **Ortam değişkenlerini ayarlayın:**
```bash
cp env.example .env
# .env dosyasını düzenleyin
```

3. **MongoDB'yi başlatın:**
```bash
# MongoDB local kurulumu gerekli
mongod
```

4. **Geliştirme sunucusunu başlatın:**
```bash
npm run dev
```

5. **Production build:**
```bash
npm run build
npm start
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Kullanıcı kaydı
- `POST /api/auth/login` - Giriş
- `POST /api/auth/logout` - Çıkış
- `POST /api/auth/forgot-password` - Şifre sıfırlama talebi
- `PUT /api/auth/reset-password/:token` - Şifre sıfırlama
- `GET /api/auth/verify-email/:token` - Email doğrulama

### Users
- `GET /api/users/profile` - Profil bilgileri
- `PUT /api/users/profile` - Profil güncelleme
- `PUT /api/users/change-password` - Şifre değiştirme
- `GET /api/users/subscription-status` - Abonelik durumu

### Subscriptions
- `GET /api/subscriptions/pricing` - Fiyatlandırma
- `POST /api/subscriptions` - Yeni abonelik
- `POST /api/subscriptions/extend` - Abonelik uzatma
- `GET /api/subscriptions/active` - Aktif abonelik
- `GET /api/subscriptions/history` - Abonelik geçmişi
- `DELETE /api/subscriptions/cancel` - Abonelik iptali

### Calculations (Abonelik Gerekli)
- `POST /api/calculations` - Yeni hesaplama
- `GET /api/calculations/my` - Kullanıcı hesaplamaları
- `GET /api/calculations/stats` - İstatistikler
- `GET /api/calculations/:id` - Tekil hesaplama
- `PUT /api/calculations/:id` - Hesaplama güncelleme
- `DELETE /api/calculations/:id` - Hesaplama silme
- `POST /api/calculations/:id/duplicate` - Hesaplama kopyalama

### Public Endpoints
- `GET /api/calculations/public` - Genel hesaplamalar
- `GET /health` - Sunucu durumu

## Modül Yapısı

```
src/
├── config/
│   └── database.ts          # MongoDB bağlantısı
├── middleware/
│   ├── auth.ts              # Kimlik doğrulama
│   └── errorHandler.ts      # Hata yakalama
├── modules/
│   ├── auth/
│   │   ├── controller.ts
│   │   └── routes.ts
│   ├── user/
│   │   ├── model.ts
│   │   ├── validation.ts
│   │   ├── service.ts
│   │   ├── controller.ts
│   │   └── routes.ts
│   ├── subscription/
│   │   ├── model.ts
│   │   ├── validation.ts
│   │   ├── service.ts
│   │   ├── controller.ts
│   │   └── routes.ts
│   └── calculation/
│       ├── model.ts
│       ├── validation.ts
│       ├── service.ts
│       ├── controller.ts
│       └── routes.ts
└── server.ts                # Ana sunucu dosyası
```

## Veritabanı Modelleri

### User
- name, email, password
- isEmailVerified, subscriptionEndDate
- Email ve şifre sıfırlama tokenları

### Subscription
- userId, subscriptionType (trial/monthly/yearly)
- startDate, endDate, isActive
- Ödeme detayları

### Calculation
- userId, calculationType
- inputData (maliyet grupları, miktarlar, fiyatlar)
- result (hesaplama sonuçları)
- title, description, tags

## Güvenlik

- **Rate Limiting**: IP başına 15 dakikada 100 istek
- **Helmet**: HTTP güvenlik başlıkları
- **CORS**: Cross-origin istekleri kontrol
- **JWT**: Güvenli token tabanlı kimlik doğrulama
- **Bcrypt**: Şifre hashleme
- **Joi**: Girdi validasyonu

## Abonelik Sistemi

1. **Deneme Süresi**: Kayıt sonrası 3 gün ücretsiz
2. **Aylık Plan**: 99 TL/ay
3. **Yıllık Plan**: 999 TL/yıl (%20 indirim)
4. **Hesaplama Erişimi**: Sadece aktif aboneler hesaplama yapabilir

## Ortam Değişkenleri

```env
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/proforma
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=7d
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
FRONTEND_URL=http://localhost:3000
```

## Scripts

- `npm run dev` - Development sunucu (nodemon)
- `npm run build` - TypeScript build
- `npm start` - Production sunucu
- `npm test` - Testleri çalıştır

## Development

1. Kodu klonlayın
2. `.env` dosyasını oluşturun
3. MongoDB'yi başlatın
4. `npm run dev` ile geliştirme sunucusunu başlatın
5. `http://localhost:4000` adresinde API erişilebilir

## Production

1. `npm run build` ile build alın
2. `dist/` klasörü oluşur
3. `npm start` ile production sunucusunu başlatın 