{"name": "proforma-backend", "version": "1.0.0", "description": "PROFORMA hesaplama uygulaması backend", "main": "src/server.ts", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "iyzipay": "^2.0.64", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "nodemailer": "^6.9.4", "pdfkit": "^0.17.1", "puppeteer": "^24.10.0", "xml2js": "^0.6.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/iyzipay": "^2.0.3", "@types/jest": "^29.5.4", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.5.0", "@types/nodemailer": "^6.4.9", "jest": "^29.6.2", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "keywords": ["proforma", "calculation", "subscription", "typescript"], "author": "OSMAN", "license": "ISC"}