'use client';

import React from 'react';

interface LighthouseAnimationProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export default function LighthouseAnimation({ className = '', size = 'md' }: LighthouseAnimationProps) {
  const sizeClasses = {
    sm: 'w-48 h-64',
    md: 'w-64 h-80',
    lg: 'w-80 h-96'
  };

  return (
    <div className={`lighthouse-container ${sizeClasses[size]} ${className}`}>
      <svg
        viewBox="0 0 400 500"
        className="w-full h-full lighthouse"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          {/* Gradients */}
          <radialGradient id="lightBeamGradient" cx="50%" cy="0%" r="100%">
            <stop offset="0%" stopColor="#FFE066" stopOpacity="0.9" />
            <stop offset="50%" stopColor="#FFC107" stopOpacity="0.6" />
            <stop offset="100%" stopColor="#FF8F00" stopOpacity="0.1" />
          </radialGradient>
          
          <linearGradient id="lighthouseGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#ffffff" />
            <stop offset="100%" stopColor="#f5f5f5" />
          </linearGradient>
          
          <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#87CEEB" />
            <stop offset="100%" stopColor="#E0F6FF" />
          </linearGradient>
          
          <linearGradient id="waterGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#0082d6" />
            <stop offset="100%" stopColor="#004d82" />
          </linearGradient>
          
          <radialGradient id="lightSourceGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#FFE066" stopOpacity="1" />
            <stop offset="70%" stopColor="#FFC107" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#FF8F00" stopOpacity="0.3" />
          </radialGradient>
        </defs>

        {/* Sky background */}
        <rect width="400" height="300" fill="url(#skyGradient)" />
        
        {/* Water */}
        <rect y="300" width="400" height="200" fill="url(#waterGradient)" className="water-waves" />
        
        {/* Clouds */}
        <g className="cloud-1">
          <ellipse cx="80" cy="80" rx="25" ry="15" fill="white" opacity="0.7" />
          <ellipse cx="95" cy="75" rx="30" ry="18" fill="white" opacity="0.7" />
          <ellipse cx="110" cy="80" rx="20" ry="12" fill="white" opacity="0.7" />
        </g>
        
        <g className="cloud-2">
          <ellipse cx="280" cy="60" rx="20" ry="12" fill="white" opacity="0.6" />
          <ellipse cx="295" cy="55" rx="25" ry="15" fill="white" opacity="0.6" />
          <ellipse cx="310" cy="60" rx="18" ry="10" fill="white" opacity="0.6" />
        </g>

        {/* Birds */}
        <g className="bird-1">
          <path d="M120 120 Q125 115 130 120 Q125 125 120 120" fill="#666" opacity="0.6" />
        </g>
        <g className="bird-2">
          <path d="M250 100 Q255 95 260 100 Q255 105 250 100" fill="#666" opacity="0.5" />
        </g>
        <g className="bird-3">
          <path d="M320 130 Q325 125 330 130 Q325 135 320 130" fill="#666" opacity="0.4" />
        </g>

        {/* Light beam (animated) */}
        <g className="light-beam">
          <polygon
            points="200,150 120,50 280,50"
            fill="url(#lightBeamGradient)"
            opacity="0.6"
          />
        </g>

        {/* Lighthouse base (rocky foundation) */}
        <ellipse cx="200" cy="320" rx="60" ry="20" fill="#8B7355" />
        
        {/* Lighthouse main structure */}
        <g transform="translate(200, 300)">
          {/* Main tower */}
          <rect x="-25" y="-150" width="50" height="150" fill="url(#lighthouseGradient)" stroke="#ddd" strokeWidth="1" />
          
          {/* Red stripes */}
          <rect x="-25" y="-130" width="50" height="15" fill="#fe9a01" />
          <rect x="-25" y="-100" width="50" height="15" fill="#fe9a01" />
          <rect x="-25" y="-70" width="50" height="15" fill="#fe9a01" />
          
          {/* Lighthouse top section (lantern room) */}
          <rect x="-20" y="-170" width="40" height="20" fill="url(#lighthouseGradient)" stroke="#ddd" strokeWidth="1" />
          
          {/* Windows in lantern room */}
          <rect x="-15" y="-165" width="8" height="10" fill="#0082d6" opacity="0.7" />
          <rect x="-4" y="-165" width="8" height="10" fill="#0082d6" opacity="0.7" />
          <rect x="7" y="-165" width="8" height="10" fill="#0082d6" opacity="0.7" />
          
          {/* Door */}
          <rect x="-8" y="-20" width="16" height="20" fill="#8B4513" />
          <circle cx="5" cy="-10" r="1" fill="#FFD700" />
          
          {/* Lighthouse dome/roof */}
          <path d="M-20 -170 Q0 -185 20 -170" fill="#fe9a01" stroke="#ddd" strokeWidth="1" />
          
          {/* Light source (animated) */}
          <g className="light-source">
            <circle cx="0" cy="-160" r="8" fill="url(#lightSourceGradient)" />
            <g className="light-source-inner">
              <circle cx="0" cy="-160" r="4" fill="#FFE066" />
            </g>
          </g>
          
          {/* Railing around lantern room */}
          <rect x="-22" y="-150" width="44" height="2" fill="#666" />
        </g>

        {/* Lighthouse reflection in water */}
        <g className="reflection" transform="translate(200, 320) scale(1, -0.6)" opacity="0.3">
          <rect x="-25" y="0" width="50" height="150" fill="url(#lighthouseGradient)" />
          <rect x="-25" y="20" width="50" height="15" fill="#fe9a01" />
          <rect x="-25" y="50" width="50" height="15" fill="#fe9a01" />
          <rect x="-25" y="80" width="50" height="15" fill="#fe9a01" />
        </g>

        {/* Water waves overlay */}
        <g className="water-waves">
          <path d="M0 320 Q100 315 200 320 T400 320 L400 500 L0 500 Z" fill="rgba(0, 130, 214, 0.1)" />
          <path d="M0 330 Q150 325 300 330 T400 330" stroke="rgba(255, 255, 255, 0.3)" strokeWidth="1" fill="none" />
          <path d="M0 340 Q100 335 200 340 T400 340" stroke="rgba(255, 255, 255, 0.2)" strokeWidth="1" fill="none" />
        </g>
      </svg>
    </div>
  );
}
