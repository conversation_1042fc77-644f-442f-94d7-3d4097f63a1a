<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Orange gradient background circle -->
  <defs>
    <radialGradient id="orangeGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFB84D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF8C00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF6B00;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E5BBA;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main pin shape -->
  <path d="M100 20 C140 20 170 50 170 90 C170 130 100 180 100 180 C100 180 30 130 30 90 C30 50 60 20 100 20 Z" fill="url(#orangeGradient)"/>
  
  <!-- Blue bottom section -->
  <path d="M100 140 C100 140 30 130 30 90 L30 120 C30 140 50 160 100 180 C150 160 170 140 170 120 L170 90 C170 130 100 140 100 140 Z" fill="url(#blueGradient)"/>
  
  <!-- Lighthouse structure -->
  <g transform="translate(100, 100)">
    <!-- Lighthouse base -->
    <rect x="-15" y="-20" width="30" height="40" fill="white" stroke="#E0E0E0" stroke-width="1"/>
    
    <!-- Lighthouse top section -->
    <rect x="-12" y="-35" width="24" height="15" fill="white" stroke="#E0E0E0" stroke-width="1"/>
    
    <!-- Lighthouse windows -->
    <rect x="-8" y="-30" width="4" height="6" fill="#FF8C00"/>
    <rect x="-2" y="-30" width="4" height="6" fill="#FF8C00"/>
    <rect x="4" y="-30" width="4" height="6" fill="#FF8C00"/>
    
    <!-- Door -->
    <rect x="-3" y="-10" width="6" height="10" fill="#FF8C00"/>
    
    <!-- Lighthouse dome -->
    <path d="M-12 -35 Q0 -45 12 -35" fill="white" stroke="#E0E0E0" stroke-width="1"/>
    
    <!-- Light beam lines (optional) -->
    <line x1="-20" y1="-40" x2="-30" y2="-45" stroke="white" stroke-width="2" opacity="0.7"/>
    <line x1="20" y1="-40" x2="30" y2="-45" stroke="white" stroke-width="2" opacity="0.7"/>
  </g>
</svg>
